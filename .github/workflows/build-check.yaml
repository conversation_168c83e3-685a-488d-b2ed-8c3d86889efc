name: Build Check

on:
  pull_request:

jobs:
  build:
    name: Build Static Files
    runs-on: ubuntu-latest
    environment: Dev
    steps:
      - name: Clone repository
        uses: actions/checkout@v4

      - name: Use Node.js 18.x
        uses: actions/setup-node@v1
        with:
          node-version: 18.x

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Write Environment Variables
        id: write_env
        run: |
          echo "
          EMAILJS_SERVICE_ID=${{ secrets.EMAILJS_SERVICE_ID }}
          EMAILJS_TEMPLATE_ID=${{ secrets.EMAILJS_TEMPLATE_ID }}
          EMAILJS_PUBLIC_KEY=${{ secrets.EMAILJS_PUBLIC_KEY }}
          " > .env
          cat .env

      - name: Install dependencies
        run: npm install

      - name: Generate build
        run: npm run build
