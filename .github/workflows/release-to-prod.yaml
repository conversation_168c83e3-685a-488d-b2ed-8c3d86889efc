name: Release to Production

on:
  workflow_dispatch:

jobs:
  build:
    name: Build Static Files
    runs-on: ubuntu-latest
    environment: Dev
    steps:
      - name: Clone repository
        uses: actions/checkout@v4

      - name: Use Node.js 18.x
        uses: actions/setup-node@v1
        with:
          node-version: 18.x

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Write Environment Variables
        id: write_env
        run: |
          echo "
          EMAILJS_SERVICE_ID=${{ secrets.EMAILJS_SERVICE_ID }}
          EMAILJS_TEMPLATE_ID=${{ secrets.EMAILJS_TEMPLATE_ID }}
          EMAILJS_PUBLIC_KEY=${{ secrets.EMAILJS_PUBLIC_KEY }}
          " > .env

      - name: Install dependencies
        run: npm install

      - name: Generate build
        run: npm run build

      - name: Upload Dist as Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ github.event.repository.name }}-${{ github.ref_name }}
          path: dist
          retention-days: 1

  deploy:
    name: Deploy static files
    needs:
      - build
    if: ${{ github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/main'  }}
    runs-on: ubuntu-latest
    environment: Dev
    steps:
      - name: Clone repository
        uses: actions/checkout@v4

      - name: Get Artifacts
        uses: actions/download-artifact@v4
        with:
          path: dist
          name: get-prepare-${{ github.ref_name }}

      - name: Configure SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan ${{ secrets.SERVER_IP }} >> ~/.ssh/known_hosts

      - name: Copy static files
        run: |
          scp -r ./dist/* ${{ secrets.SERVER_USERNAME }}@${{ secrets.SERVER_IP }}:${{ secrets.PROJECT_PATH_PROD }}
          echo "Build Pass"
