# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local
PROJECT_FLOW.md
DASHBOARD_GUIDE.md
Swagger Schema.yaml
PAGINATION_IMPLEMENTATION.md
maplibre-map
.env.example
SENTRY_SETUP.md
dev-dist


# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
docs/i18n-implementation.md
docs/search-functionality.md
.vercel/project.json
