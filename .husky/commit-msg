#!/bin/sh
if head -1 "$1" | grep -qE "Merge .{1,}$"; then
    echo $'\e[1;36m Skipping commit check.\e[0m\n'
    exit 0
fi
if ! head -1 "$1" | grep -qE "^(feat|fix|chore|docs|test|style|refactor|perf|build|ci|revert|hotfix|asap)(\(.+?\))?: .{1,}$"; then
    echo $'\e[1;31m Aborting commit. Your commit message is invalid.\e[0m\n' >&2
    echo $'\e[1;36m Please fix your commit message starting with prefix-\e[0m \e[36m\n feat|fix|chore|docs|test|style|refactor|perf|build|ci|revert|hotfix|asap \n followed by ":" and what work did you do.\e[0m \n for example: \e[1;42m feat: your commit message \e[0m\n'
    exit 1
fi
if ! head -1 "$1" | grep -qE "^.{1,200}$"; then
    echo $'\n\e[1;31m Aborting commit. Your commit message is too long.\n\e[0m' >&2
    echo $'\e[42m Please keep your commit message short i.e. only upto 200 characters.\e[0m\n'
    exit 1
fi
