# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type-aware lint rules:

```js
export default tseslint.config({
  extends: [
    // Remove ...tseslint.configs.recommended and replace with this
    ...tseslint.configs.recommendedTypeChecked,
    // Alternatively, use this for stricter rules
    ...tseslint.configs.strictTypeChecked,
    // Optionally, add this for stylistic rules
    ...tseslint.configs.stylisticTypeChecked,
  ],
  languageOptions: {
    // other options...
    parserOptions: {
      project: ["./tsconfig.node.json", "./tsconfig.app.json"],
      tsconfigRootDir: import.meta.dirname,
    },
  },
});
```

You can also install [eslint-plugin-react-x](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-x) and [eslint-plugin-react-dom](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-dom) for React-specific lint rules:

```js
// eslint.config.js
import reactX from "eslint-plugin-react-x";
import reactDom from "eslint-plugin-react-dom";

export default tseslint.config({
  plugins: {
    // Add the react-x and react-dom plugins
    "react-x": reactX,
    "react-dom": reactDom,
  },
  rules: {
    // other rules...
    // Enable its recommended typescript rules
    ...reactX.configs["recommended-typescript"].rules,
    ...reactDom.configs.recommended.rules,
  },
});
```

## EmailJS Setup Guide

To send emails via EmailJS in the project, follow the steps below to get your API keys:

---

### Step 1: Log In or Sign Up

Go to:[https://www.emailjs.com](https://www.emailjs.com)  
Create a free account or log in to your existing one.

---

### Step 2: Create an Email Service

Go to dashboard:[https://dashboard.emailjs.com](https://dashboard.emailjs.com)

1. In the left sidebar, go to **Email Services**
2. Click **“Add New Service”**
3. Choose your email provider (e.g., Gmail)
4. Authorize it and finish setup
5. Copy your **Service ID**

> Example: `service_ii*****`

---

### Step 3: Create an Email Template

1. Go to **Email Templates**
2. Click **“Create New Template”**
3. Add variables like `name`, `email`, `message`, etc.
4. Customize layout and save it
5. Copy your **Template ID**

> Example: `template_7o2ezyl`

---

### Step 4: Get Your Public Key

1. Go to **Account → API Keys**
2. Copy the **Public Key (User ID)**

> Example: `R6LT1xs**********`

---

### Step 5: Store Keys in `.env`

Create or update your `.env` file at the root of your project:

```env
EMAILJS_SERVICE_ID=your_service_id
EMAILJS_TEMPLATE_ID=your_template_id
EMAILJS_PUBLIC_KEY=your_public_key
```
