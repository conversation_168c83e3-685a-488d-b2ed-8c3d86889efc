<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />


    <title>getPrepare - Disaster Risk Management</title>
    <meta name="title" content="getPrepare - Disaster Risk Management" />
    <meta name="description" content="A comprehensive disaster risk management and monitoring application for Nepal. Monitor real-time weather, water levels, and receive critical alerts." />
    <meta name="keywords" content="disaster management, Nepal, weather monitoring, flood alerts, risk assessment, emergency preparedness" />
    <meta name="author" content="getPrepare Team" />


    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, minimum-scale=1.0" />



    <meta name="theme-color" content="#1D8E89" />
    <meta name="msapplication-TileColor" content="#1D8E89" />
    <meta name="msapplication-navbutton-color" content="#1D8E89" />


    <link rel="icon" type="image/svg+xml" href="/Favicon.svg" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />



    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://getprepare.com/" />
    <meta property="og:title" content="getPrepare - Disaster Risk Management" />
    <meta property="og:description" content="A comprehensive disaster risk management and monitoring application for Nepal. Monitor real-time weather, water levels, and receive critical alerts." />
    <meta property="og:image" content="/icons/icon-512x512.png" />


    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://getprepare.com/" />
    <meta property="twitter:title" content="getPrepare - Disaster Risk Management" />
    <meta property="twitter:description" content="A comprehensive disaster risk management and monitoring application for Nepal. Monitor real-time weather, water levels, and receive critical alerts." />
    <meta property="twitter:image" content="/icons/icon-512x512.png" />


    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://getprepare-api.optisyslab.com" />


    <link
      href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined "
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20,400,0,0"
    />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
