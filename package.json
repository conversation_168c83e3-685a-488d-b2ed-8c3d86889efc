{"name": "get-prepare", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "prepare": "husky install", "preview": "vite preview", "lint:fix": "eslint --fix ."}, "dependencies": {"@emailjs/browser": "^4.4.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/react": "^9.40.0", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@turf/boolean-point-in-polygon": "^7.2.0", "@turf/turf": "^7.2.0", "@types/react-router-dom": "^5.3.3", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "husky": "^9.1.7", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.513.0", "maplibre-gl": "^5.6.1", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.1", "react-phone-input-2": "^2.15.1", "react-router": "^7.6.2", "react-router-dom": "^6.30.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.30", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "sharp": "^0.34.3", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["eslint --fix", "prettier --write"]}, "engines": {"npm": ">=8.0.0", "node": ">=16.0.0"}}