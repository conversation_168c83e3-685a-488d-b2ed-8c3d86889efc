<svg width="216" height="154" viewBox="0 0 216 154" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M126.441 44.6545C113.135 44.7339 99.6222 44.1146 86.983 40.6055C74.3438 37.0963 63.1654 30.4751 53.0032 22.7423C46.3501 17.7088 40.3005 13.7075 31.6626 14.3108C23.2169 14.7547 15.1385 17.9059 8.62303 23.298C-2.36482 32.9203 -0.697589 50.7041 3.68485 63.2163C10.2903 82.064 30.3923 95.1478 47.4616 103.675C67.1985 113.535 88.8725 119.267 110.61 122.554C129.664 125.444 154.133 127.556 170.63 115.107C185.794 103.69 189.954 77.5863 186.239 59.9613C185.335 54.7555 182.563 50.0584 178.443 46.7504C167.788 38.97 151.91 44.1622 139.937 44.4322C135.571 44.5274 130.982 44.6386 126.441 44.6545Z" fill="#F2F2F2"/>
<path d="M191.098 119.125V125.953" stroke="#CFCFCF" stroke-width="1.34966" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M187.684 122.539H194.511" stroke="#CFCFCF" stroke-width="1.34966" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.9721 89.639C10.8315 89.639 11.5282 88.9423 11.5282 88.0829C11.5282 87.2235 10.8315 86.5269 9.9721 86.5269C9.1127 86.5269 8.41602 87.2235 8.41602 88.0829C8.41602 88.9423 9.1127 89.639 9.9721 89.639Z" fill="#CFCFCF"/>
<path d="M99.3363 153.423C131.52 153.423 157.61 151.802 157.61 149.802C157.61 147.803 131.52 146.182 99.3363 146.182C67.1526 146.182 41.0625 147.803 41.0625 149.802C41.0625 151.802 67.1526 153.423 99.3363 153.423Z" fill="#F2F2F2"/>
<path d="M54.4482 1.57715H139.08C141.186 1.57715 143.205 2.4136 144.694 3.90249C146.183 5.39138 147.019 7.41074 147.019 9.51635V110.201C147.019 112.307 146.183 114.326 144.694 115.815C143.205 117.304 141.186 118.141 139.08 118.141H43.6033C41.4977 118.141 39.4783 117.304 37.9894 115.815C36.5005 114.326 35.6641 112.307 35.6641 110.201V20.5518L54.4482 1.57715Z" fill="white" stroke="#BABABA" stroke-width="1.58784" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M146.638 50.6406H59.3701V119.108H146.638V50.6406Z" fill="#D2D2D2"/>
<path d="M35.6641 20.5518H51.0502C51.9529 20.5476 52.8172 20.1861 53.4539 19.5464C54.0907 18.9066 54.4482 18.0407 54.4482 17.138V1.57715L35.6641 20.5518Z" fill="white" stroke="#BABABA" stroke-width="1.58784" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M146.209 28.7769H59.8939C56.5353 28.7769 53.8125 31.4996 53.8125 34.8583V53.3249C53.8125 56.6836 56.5353 59.4063 59.8939 59.4063H146.209C149.568 59.4063 152.29 56.6836 152.29 53.3249V34.8583C152.29 31.4996 149.568 28.7769 146.209 28.7769Z" fill="white" stroke="#BABABA" stroke-width="1.58784" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M70.5807 49.4344C73.5097 49.4344 75.8841 47.06 75.8841 44.131C75.8841 41.202 73.5097 38.8276 70.5807 38.8276C67.6518 38.8276 65.2773 41.202 65.2773 44.131C65.2773 47.06 67.6518 49.4344 70.5807 49.4344Z" fill="#D2D2D2"/>
<path d="M87.2526 49.4344C90.1816 49.4344 92.556 47.06 92.556 44.131C92.556 41.202 90.1816 38.8276 87.2526 38.8276C84.3236 38.8276 81.9492 41.202 81.9492 44.131C81.9492 47.06 84.3236 49.4344 87.2526 49.4344Z" fill="#D2D2D2"/>
<path d="M103.909 49.4344C106.838 49.4344 109.212 47.06 109.212 44.131C109.212 41.202 106.838 38.8276 103.909 38.8276C100.98 38.8276 98.6055 41.202 98.6055 44.131C98.6055 47.06 100.98 49.4344 103.909 49.4344Z" fill="#D2D2D2"/>
<path d="M146.209 62.7246H59.8939C56.5353 62.7246 53.8125 65.4474 53.8125 68.806V87.2726C53.8125 90.6313 56.5353 93.3541 59.8939 93.3541H146.209C149.568 93.3541 152.29 90.6313 152.29 87.2726V68.806C152.29 65.4474 149.568 62.7246 146.209 62.7246Z" fill="white" stroke="#BABABA" stroke-width="1.58784" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M70.5807 83.3978C73.5097 83.3978 75.8841 81.0234 75.8841 78.0944C75.8841 75.1654 73.5097 72.791 70.5807 72.791C67.6518 72.791 65.2773 75.1654 65.2773 78.0944C65.2773 81.0234 67.6518 83.3978 70.5807 83.3978Z" fill="#D2D2D2"/>
<path d="M87.2526 83.3978C90.1816 83.3978 92.556 81.0234 92.556 78.0944C92.556 75.1654 90.1816 72.791 87.2526 72.791C84.3236 72.791 81.9492 75.1654 81.9492 78.0944C81.9492 81.0234 84.3236 83.3978 87.2526 83.3978Z" fill="#D2D2D2"/>
<path d="M103.909 83.3978C106.838 83.3978 109.212 81.0234 109.212 78.0944C109.212 75.1654 106.838 72.791 103.909 72.791C100.98 72.791 98.6055 75.1654 98.6055 78.0944C98.6055 81.0234 100.98 83.3978 103.909 83.3978Z" fill="#D2D2D2"/>
<path d="M146.209 96.688H59.8939C56.5353 96.688 53.8125 99.4107 53.8125 102.769V121.236C53.8125 124.595 56.5353 127.317 59.8939 127.317H146.209C149.568 127.317 152.29 124.595 152.29 121.236V102.769C152.29 99.4107 149.568 96.688 146.209 96.688Z" fill="white" stroke="#BABABA" stroke-width="1.58784" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M70.5807 117.346C73.5097 117.346 75.8841 114.972 75.8841 112.043C75.8841 109.114 73.5097 106.739 70.5807 106.739C67.6518 106.739 65.2773 109.114 65.2773 112.043C65.2773 114.972 67.6518 117.346 70.5807 117.346Z" fill="#D2D2D2"/>
<path d="M87.2526 117.346C90.1816 117.346 92.556 114.972 92.556 112.043C92.556 109.114 90.1816 106.739 87.2526 106.739C84.3236 106.739 81.9492 109.114 81.9492 112.043C81.9492 114.972 84.3236 117.346 87.2526 117.346Z" fill="#D2D2D2"/>
<path d="M103.909 117.346C106.838 117.346 109.212 114.972 109.212 112.043C109.212 109.114 106.838 106.739 103.909 106.739C100.98 106.739 98.6055 109.114 98.6055 112.043C98.6055 114.972 100.98 117.346 103.909 117.346Z" fill="#D2D2D2"/>
<path d="M154.021 67.4569C172.2 67.4569 186.937 52.7199 186.937 34.5409C186.937 16.362 172.2 1.625 154.021 1.625C135.842 1.625 121.105 16.362 121.105 34.5409C121.105 52.7199 135.842 67.4569 154.021 67.4569Z" fill="white" stroke="#BABABA" stroke-width="1.58784" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M177.395 58.4214L186.159 67.1863" stroke="#BABABA" stroke-width="1.58784" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M185.181 62.9308L182.46 65.7772C181.235 67.0577 181.281 69.088 182.562 70.3121L206.206 92.9146C207.486 94.1387 209.516 94.093 210.74 92.8125L213.462 89.9661C214.686 88.6856 214.64 86.6553 213.359 85.4312L189.715 62.8287C188.435 61.6046 186.405 61.6503 185.181 62.9308Z" fill="white" stroke="#BABABA" stroke-width="1.58784" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
