import "./index.css";
import GenerateRoutes from "./generateRoutes";
import appRoutes from "./routes";
import ScrollToTopOnRouteUpdate from "./_lib/ScrollToTop";
import { ErrorBoundary } from "@sentry/react";

function App() {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <svg
                className="h-6 w-6 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h1 className="mb-2 text-center text-xl font-semibold text-gray-900">
              Something went wrong
            </h1>
            <p className="mb-6 text-center text-gray-600">
              We've been notified about this error and will fix it soon.
            </p>
            <div className="flex flex-col space-y-3">
              <button
                onClick={resetError}
                className="w-full rounded-md bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
              >
                Try again
              </button>
              <button
                onClick={() => (window.location.href = "/")}
                className="w-full rounded-md bg-gray-200 px-4 py-2 text-gray-800 transition-colors hover:bg-gray-300"
              >
                Go to homepage
              </button>
            </div>
            {import.meta.env.MODE === "development" && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm text-gray-500">
                  Error details (dev only)
                </summary>
                <pre className="mt-2 overflow-auto rounded bg-red-50 p-2 text-xs text-red-600">
                  {error instanceof Error ? error.toString() : String(error)}
                </pre>
              </details>
            )}
          </div>
        </div>
      )}
      showDialog={false}
    >
      <ScrollToTopOnRouteUpdate />
      <div>
        <GenerateRoutes routes={appRoutes} />
      </div>
    </ErrorBoundary>
  );
}

export default App;
