import { useEffect } from "react";
import { useLocation } from "react-router-dom";

function ScrollToTopOnRouteUpdate() {
  const { pathname, hash } = useLocation();
  useEffect(() => {
    if (!hash) {
      const timer = setTimeout(() => {
        window.scrollTo({ top: 0, behavior: "smooth" });
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [pathname, hash]);

  return null;
}

export default ScrollToTopOnRouteUpdate;
