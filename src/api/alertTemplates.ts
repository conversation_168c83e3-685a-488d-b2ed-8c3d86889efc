import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { axiosInstance } from "@/lib/axios";
import { API_ROUTES } from "@/config/api";
import { PaginationParams, PaginatedResponse } from "@/lib/utils";
import type {
  AlertTemplate,
  AlertTemplateRequest,
  TemplateType,
} from "@/types/alertTemplate";


export const alertTemplateKeys = {
  all: ["alertTemplates"] as const,
  lists: () => [...alertTemplateKeys.all, "list"] as const,
  detail: (id: number) => [...alertTemplateKeys.all, "detail", id] as const,
};


export const useAlertTemplates = () => {
  return useQuery({
    queryKey: alertTemplateKeys.lists(),
    queryFn: async () => {
      let allTemplates: AlertTemplate[] = [];
      let currentPage = 1;
      let hasNextPage = true;

      while (hasNextPage) {
        const { data } = await axiosInstance.get(
          `${API_ROUTES.alertTemplate.list}?page=${currentPage}&page_size=50`
        );

        if (data.results && Array.isArray(data.results)) {
          allTemplates = [...allTemplates, ...data.results];
          hasNextPage = !!data.next;
          currentPage++;
        } else if (Array.isArray(data)) {
          allTemplates = data;
          hasNextPage = false;
        } else {
          hasNextPage = false;
        }
      }

      return allTemplates;
    },
  });
};


const buildPaginationQuery = (params: PaginationParams): string => {
  const searchParams = new URLSearchParams();
  if (params.page) searchParams.append('page', params.page.toString());
  if (params.page_size) searchParams.append('page_size', params.page_size.toString());
  return searchParams.toString() ? `?${searchParams.toString()}` : '';
};


export const fetchAlertTemplatesPaginated = async (params?: PaginationParams): Promise<PaginatedResponse<AlertTemplate>> => {
  try {
    const queryString = buildPaginationQuery(params || {});
    const url = `${API_ROUTES.alertTemplate.list}${queryString}`;
    const response = await axiosInstance.get(url);

    let templatesData: AlertTemplate[];
    let totalCount: number;
    let hasNext: boolean = false;
    let hasPrevious: boolean = false;

    if (Array.isArray(response.data)) {
      templatesData = response.data;
      totalCount = response.data.length;

      const page = params?.page || 1;
      const pageSize = params?.page_size || 10;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;

      templatesData = response.data.slice(startIndex, endIndex);
      hasNext = endIndex < response.data.length;
      hasPrevious = page > 1;
    } else {
      templatesData = response.data.results || [];
      totalCount = response.data.count || 0;
      hasNext = !!response.data.next;
      hasPrevious = !!response.data.previous;
    }

    return {
      count: totalCount,
      next: hasNext ? 'next' : null,
      previous: hasPrevious ? 'previous' : null,
      results: templatesData,
    };
  } catch (error) {
    console.error('Error fetching paginated alert templates:', error);
    throw error;
  }
};


export const fetchAlertTemplatesByTypePaginated = async (
  templateType: TemplateType,
  params?: PaginationParams
): Promise<PaginatedResponse<AlertTemplate>> => {
  try {
    const allTemplatesResponse = await fetchAlertTemplatesPaginated({ page: 1, page_size: 1000 });

    const filteredTemplates = allTemplatesResponse.results.filter(
      template => template.template_type === templateType
    );

    const page = params?.page || 1;
    const pageSize = params?.page_size || 10;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedTemplates = filteredTemplates.slice(startIndex, endIndex);

    return {
      count: filteredTemplates.length,
      next: endIndex < filteredTemplates.length ? 'next' : null,
      previous: page > 1 ? 'previous' : null,
      results: paginatedTemplates,
    };
  } catch (error) {
    console.error('Error fetching paginated alert templates by type:', error);
    throw error;
  }
};


export const fetchAllAlertTemplatesDirect = async (): Promise<AlertTemplate[]> => {
  try {
    const response = await axiosInstance.get<AlertTemplate[] | { results: AlertTemplate[] }>(API_ROUTES.alertTemplate.list);
    return Array.isArray(response.data) ? response.data : (response.data as { results: AlertTemplate[] }).results || [];
  } catch (error) {
    throw error;
  }
};


export const useAlertTemplate = (id: number) => {
  return useQuery({
    queryKey: alertTemplateKeys.detail(id),
    queryFn: async () => {
      const { data } = await axiosInstance.get<AlertTemplate>(
        API_ROUTES.alertTemplate.getById(id),
      );
      return data;
    },
    enabled: !!id,
  });
};


export const useCreateAlertTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (template: AlertTemplateRequest) => {
      const { data } = await axiosInstance.post<AlertTemplate>(
        API_ROUTES.alertTemplate.create,
        template,
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: alertTemplateKeys.lists() });
    },
  });
};


export const useUpdateAlertTemplate = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (template: AlertTemplateRequest) => {
      const { data } = await axiosInstance.patch<AlertTemplate>(
        API_ROUTES.alertTemplate.update(id),
        template,
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: alertTemplateKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: alertTemplateKeys.lists() });
    },
  });
};


export const useDeleteAlertTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number) => {
      await axiosInstance.delete(API_ROUTES.alertTemplate.delete(id));
    },
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: alertTemplateKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: alertTemplateKeys.lists() });
    },
  });
};
