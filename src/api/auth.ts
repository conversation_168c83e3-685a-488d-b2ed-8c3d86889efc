import { useMutation, useQuery } from "@tanstack/react-query";
import { axiosInstance } from "@/lib/axios";
import { API_ROUTES } from "@/config/api";
import type {
  LoginCredentials,
  TokenResponse,
  User,
  UserRequest,
} from "@/types/auth";


export const TOKEN_STORAGE_KEYS = {
  ACCESS_TOKEN: "accessToken",
  REFRESH_TOKEN: "refreshToken",
} as const;


export const authKeys = {
  user: ["user"] as const,
  userById: (id: number) => ["user", id] as const,
};


export const useLogin = () => {
  return useMutation({
    mutationFn: async (credentials: LoginCredentials) => {
      const { data } = await axiosInstance.post<TokenResponse>(
        API_ROUTES.auth.login,
        credentials,
      );
      return data;
    },
  });
};


export const useCurrentUser = () => {
  return useQuery({
    queryKey: authKeys.user,
    queryFn: async () => {
      const { data } = await axiosInstance.get<User>(API_ROUTES.auth.protected);
      return data;
    },
    retry: false,
    enabled: !!localStorage.getItem(TOKEN_STORAGE_KEYS.ACCESS_TOKEN),
  });
};


export const useUser = (id: number) => {
  return useQuery({
    queryKey: authKeys.userById(id),
    queryFn: async () => {
      const { data } = await axiosInstance.get<User>(
        API_ROUTES.auth.userById(id),
      );
      return data;
    },
  });
};


export const useCreateUser = () => {
  return useMutation({
    mutationFn: async (userData: UserRequest) => {
      const { data } = await axiosInstance.post<User>(
        API_ROUTES.auth.user,
        userData,
      );
      return data;
    },
  });
};
