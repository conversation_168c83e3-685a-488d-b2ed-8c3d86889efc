import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { API_ROUTES } from "@/config/api";
import { axiosInstance } from "@/lib/axios";
import { GroupRequest, GroupResponse, Group } from "@/types/group";
import { PaginationParams, PaginatedResponse } from "@/lib/utils";

interface GroupFilters {
  category?: string;
  search?: string;
  page?: number;
  page_size?: number;
}


export const groupKeys = {
  all: ["groups"] as const,
  lists: () => [...groupKeys.all, "list"] as const,
  list: (filters: GroupFilters) =>
    [...groupKeys.lists(), { ...filters }] as const,
  detail: (id: number) => [...groupKeys.all, "detail", id] as const,
};


export const useGetGroups = (filters?: GroupFilters) => {
  return useQuery({
    queryKey: groupKeys.list(filters || {}),
    queryFn: async () => {
      const params = new URLSearchParams();

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, String(value));
          }
        });
      }

      const { data } = await axiosInstance.get(
        `${API_ROUTES.groups.list}${params.toString() ? `?${params.toString()}` : ""}`,
      );
      return data;
    },
  });
};


export const useCreateGroup = () => {
  return useMutation({
    mutationFn: async (group: GroupRequest): Promise<GroupResponse> => {
      const formData = new FormData();

      Object.entries(group).forEach(([key, value]) => {
        if (key !== "contacts_file") {
          formData.append(key, value as string);
        }
      });

      if (group.contacts_file) {
        formData.append("contacts_file", group.contacts_file);
      }

      const { data } = await axiosInstance.post(
        API_ROUTES.groups.create,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );
      return data;
    },
  });
};


export const useDownloadTemplate = () => {
  return useMutation({
    mutationFn: async (): Promise<Blob> => {
      const response = await axiosInstance.get(
        API_ROUTES.groups.downloadTemplate,
        {
          responseType: "blob",
        },
      );
      return response.data;
    },
  });
};


export const useGetGroup = (id: number) => {
  return useQuery({
    queryKey: groupKeys.detail(id),
    queryFn: async () => {
      const { data } = await axiosInstance.get<Group>(
        API_ROUTES.groups.getById(id),
      );
      return data;
    },
    enabled: !!id,
  });
};


export const useUpdateGroup = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (group: Partial<GroupRequest>): Promise<GroupResponse> => {
      const formData = new FormData();


      Object.entries(group).forEach(([key, value]) => {
        if (key !== "contacts_file" && value !== undefined && value !== null) {
          formData.append(key, value as string);
        }
      });


      if (group.contacts_file) {
        formData.append("contacts_file", group.contacts_file);
      }

      const { data } = await axiosInstance.patch(
        API_ROUTES.groups.update(id),
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );
      return data;
    },
    onSuccess: () => {

      queryClient.invalidateQueries({ queryKey: groupKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: groupKeys.lists() });
    },
  });
};


export const useDeleteGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number) => {
      await axiosInstance.delete(API_ROUTES.groups.delete(id));
    },
    onSuccess: (_, id) => {

      queryClient.removeQueries({ queryKey: groupKeys.detail(id) });

      queryClient.invalidateQueries({ queryKey: groupKeys.lists() });
    },
  });
};


const buildPaginationQuery = (params: PaginationParams & GroupFilters): string => {
  const searchParams = new URLSearchParams();
  if (params.page) searchParams.append('page', params.page.toString());
  if (params.page_size) searchParams.append('page_size', params.page_size.toString());
  if (params.category) searchParams.append('category', params.category);
  if (params.search) searchParams.append('search', params.search);
  return searchParams.toString() ? `?${searchParams.toString()}` : '';
};


export const fetchAllGroupsDirect = async (filters?: GroupFilters): Promise<Group[]> => {
  try {
    const params = new URLSearchParams();


    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, String(value));
        }
      });
    }

    const response = await axiosInstance.get(
      `${API_ROUTES.groups.list}${params.toString() ? `?${params.toString()}` : ""}`
    );


    if (Array.isArray(response.data)) {
      return response.data;
    } else if (response.data && response.data.results) {
      return response.data.results;
    } else {
      return response.data || [];
    }
  } catch (error) {
    throw error;
  }
};


export const fetchGroupsPaginated = async (
  params?: PaginationParams & GroupFilters
): Promise<PaginatedResponse<Group>> => {
  try {
    const queryString = buildPaginationQuery(params || {});
    const url = `${API_ROUTES.groups.list}${queryString}`;
    const response = await axiosInstance.get(url);


    let groupsData: Group[];
    let totalCount: number;
    let hasNext: boolean = false;
    let hasPrevious: boolean = false;

    if (Array.isArray(response.data)) {

      groupsData = response.data;
      totalCount = response.data.length;


      const page = params?.page || 1;
      const pageSize = params?.page_size || 10;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;

      groupsData = response.data.slice(startIndex, endIndex);
      hasNext = endIndex < response.data.length;
      hasPrevious = page > 1;
    } else {

      groupsData = response.data.results || [];
      totalCount = response.data.count || 0;
      hasNext = !!response.data.next;
      hasPrevious = !!response.data.previous;
    }

    return {
      count: totalCount,
      next: hasNext ? 'next' : null,
      previous: hasPrevious ? 'previous' : null,
      results: groupsData,
    };
  } catch (error) {
    throw error;
  }
};
