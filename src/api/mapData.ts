import { axiosInstance } from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";
import {
  PaginatedResponse,
  PaginationParams,
  buildPaginationQuery,
} from "@/lib/utils";

function extractCoordinatesFromGeom(
  geom: string | undefined,
): [number, number] {
  let longitude = 0;
  let latitude = 0;

  if (geom) {
    try {
      const geomMatch = geom.match(/POINT\s*\(([\d.]+)\s+([\d.]+)\)/i);

      if (geomMatch && geomMatch.length === 3) {
        longitude = parseFloat(geomMatch[1]);
        latitude = parseFloat(geomMatch[2]);

        if (
          isNaN(longitude) ||
          isNaN(latitude) ||
          longitude < -180 ||
          longitude > 180 ||
          latitude < -90 ||
          latitude > 90
        ) {
          longitude = 0;
          latitude = 0;
        }
      }
    } catch {
      // Ignore parsing errors, return default coordinates
    }
  }

  return [longitude, latitude];
}

export interface StationData {
  id: number;
  series_id: number;
  stationIndex: string;
  name: string;
  status: string;
  basin: string;
  district: string;
  description: string;
  longitude: number;
  latitude: number;
  value: number | null;
  interval: number | null;
  blink: boolean;
  warningLevel?: number | null;
  dangerLevel?: number | null;

  rainfall1h?: number | null;
  rainfall3h?: number | null;
  rainfall6h?: number | null;
  rainfall12h?: number | null;
  rainfall24h?: number | null;
}

interface BackendRainfallResponse {
  id: number;
  station_id: string;
  station_name: string;
  district: string;
  basin: string;
  rainfall_1h: number;
  rainfall_3h: number;
  rainfall_6h: number;
  rainfall_12h: number;
  rainfall_24h: number;
  status: string;
  last_updated: string;
  geom: string;
}

export const fetchRainfallDataPaginated = async (
  params?: PaginationParams,
): Promise<PaginatedResponse<StationData>> => {
  try {
    const queryString = buildPaginationQuery(params || {});
    const url = `https://getprepare-api.optisyslab.com/api/v1/rainfall-stations/${queryString}`;
    const response = await axiosInstance.get(url);

    let stationsData: BackendRainfallResponse[];
    let totalCount: number;
    let hasNext: boolean = false;
    let hasPrevious: boolean = false;

    if (Array.isArray(response.data)) {
      stationsData = response.data;
      totalCount = response.data.length;

      const page = params?.page || 1;
      const pageSize = params?.page_size || 50;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;

      stationsData = response.data.slice(startIndex, endIndex);
      hasNext = endIndex < response.data.length;
      hasPrevious = page > 1;
    } else {
      stationsData = response.data.results || [];
      totalCount = response.data.count || 0;
      hasNext = !!response.data.next;
      hasPrevious = !!response.data.previous;
    }

    const stations: StationData[] = stationsData.map((station, index) => {
      const [longitude, latitude] = extractCoordinatesFromGeom(station.geom);

      return {
        id: station.id || index,
        series_id: parseInt(station.station_id) || 0,
        stationIndex: station.station_id,
        name: station.station_name,
        status: station.status,
        basin: station.basin,
        district: station.district,
        description: `Rainfall data from station ${station.station_name}`,
        longitude,
        latitude,
        value: station.rainfall_24h,
        interval: null,
        blink: station.status === "DANGER LEVEL",
        rainfall1h: station.rainfall_1h,
        rainfall3h: station.rainfall_3h,
        rainfall6h: station.rainfall_6h,
        rainfall12h: station.rainfall_12h,
        rainfall24h: station.rainfall_24h,
      };
    });

    return {
      count: totalCount,
      next: hasNext
        ? `${url.split("?")[0]}?page=${(params?.page || 1) + 1}&page_size=${params?.page_size || 50}`
        : null,
      previous: hasPrevious
        ? `${url.split("?")[0]}?page=${(params?.page || 1) - 1}&page_size=${params?.page_size || 50}`
        : null,
      results: stations,
    };
  } catch (error) {
    console.error("Error fetching paginated rainfall data:", error);
    throw error;
  }
};

export const fetchAllRainfallDataDirect = async (): Promise<StationData[]> => {
  const response = await axiosInstance.get("/api/v1/rainfall-stations/");

  let allRainfallData: BackendRainfallResponse[] = [];

  if (Array.isArray(response.data)) {
    allRainfallData = response.data;
  } else if (response.data.results) {
    allRainfallData = response.data.results;

    let nextUrl = response.data.next;
    while (nextUrl) {
      const nextResponse = await axiosInstance.get(nextUrl);
      allRainfallData.push(...nextResponse.data.results);
      nextUrl = nextResponse.data.next;
    }
  }

  const stations: StationData[] = allRainfallData.map((station, index) => {
    const [longitude, latitude] = extractCoordinatesFromGeom(station.geom);

    return {
      id: station.id || index,
      series_id: parseInt(station.station_id) || 0,
      stationIndex: station.station_id,
      name: station.station_name,
      status: station.status,
      basin: station.basin,
      district: station.district,
      description: `Rainfall data from station ${station.station_name}`,
      longitude,
      latitude,
      value: station.rainfall_24h,
      interval: null,
      blink: station.status === "DANGER LEVEL",
      rainfall1h: station.rainfall_1h,
      rainfall3h: station.rainfall_3h,
      rainfall6h: station.rainfall_6h,
      rainfall12h: station.rainfall_12h,
      rainfall24h: station.rainfall_24h,
    };
  });

  return stations;
};

export const fetchRainfallData = async (): Promise<{
  stations: StationData[];
  timestamp: string;
}> => {
  const allStations = await fetchAllRainfallDataDirect();
  const timestamp = new Date().toLocaleString();

  return {
    stations: allStations,
    timestamp,
  };
};

interface BackendWaterLevelResponse {
  id: number;
  station_id: string;
  station_name: string;
  station_index: string;
  district: string;
  basin: string;
  water_level: number;
  warning_level: number;
  danger_level: number;
  status: string;
  level_updated: string;
  last_updated: string;
  geom: string;
}

export const fetchWaterLevelDataPaginated = async (
  params?: PaginationParams,
): Promise<PaginatedResponse<StationData>> => {
  try {
    const queryString = buildPaginationQuery(params || {});
    const url = `https://getprepare-api.optisyslab.com/api/v1/waterlevel-stations/${queryString}`;
    const response = await axiosInstance.get(url);

    let stationsData: BackendWaterLevelResponse[];
    let totalCount: number;
    let hasNext: boolean = false;
    let hasPrevious: boolean = false;

    if (Array.isArray(response.data)) {
      stationsData = response.data;
      totalCount = response.data.length;

      const page = params?.page || 1;
      const pageSize = params?.page_size || 50;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;

      stationsData = response.data.slice(startIndex, endIndex);
      hasNext = endIndex < response.data.length;
      hasPrevious = page > 1;
    } else {
      stationsData = response.data.results || [];
      totalCount = response.data.count || 0;
      hasNext = !!response.data.next;
      hasPrevious = !!response.data.previous;
    }

    const stations: StationData[] = stationsData.map((station, index) => {
      const [longitude, latitude] = extractCoordinatesFromGeom(station.geom);

      return {
        id: station.id || index,
        series_id: parseInt(station.station_id) || 0,
        stationIndex: station.station_index || "",
        name: station.station_name,
        status: station.status,
        basin: station.basin,
        district: station.district,
        description: `Warning: ${station.warning_level || "N/A"}, Danger: ${station.danger_level || "N/A"}`,
        longitude,
        latitude,
        value: station.water_level,
        interval: null,
        blink: station.status === "DANGER LEVEL",
        warningLevel: station.warning_level,
        dangerLevel: station.danger_level,
      };
    });

    return {
      count: totalCount,
      next: hasNext
        ? `${url.split("?")[0]}?page=${(params?.page || 1) + 1}&page_size=${params?.page_size || 50}`
        : null,
      previous: hasPrevious
        ? `${url.split("?")[0]}?page=${(params?.page || 1) - 1}&page_size=${params?.page_size || 50}`
        : null,
      results: stations,
    };
  } catch (error) {
    console.error("Error fetching paginated water level data:", error);
    throw error;
  }
};

export const fetchAllWaterLevelDataDirect = async (): Promise<
  StationData[]
> => {
  const response = await axiosInstance.get("/api/v1/waterlevel-stations/");

  let allWaterLevelData: BackendWaterLevelResponse[] = [];

  if (Array.isArray(response.data)) {
    allWaterLevelData = response.data;
  } else if (response.data.results) {
    allWaterLevelData = response.data.results;

    let nextUrl = response.data.next;
    while (nextUrl) {
      const nextResponse = await axiosInstance.get(nextUrl);
      allWaterLevelData.push(...nextResponse.data.results);
      nextUrl = nextResponse.data.next;
    }
  }

  const stations: StationData[] = allWaterLevelData.map((station, index) => {
    const [longitude, latitude] = extractCoordinatesFromGeom(station.geom);

    return {
      id: station.id || index,
      series_id: parseInt(station.station_id) || 0,
      stationIndex: station.station_index || "",
      name: station.station_name,
      status: station.status,
      basin: station.basin,
      district: station.district,
      description: `Warning: ${station.warning_level || "N/A"}, Danger: ${station.danger_level || "N/A"}`,
      longitude,
      latitude,
      value: station.water_level,
      interval: null,
      blink: station.status === "DANGER LEVEL",
      warningLevel: station.warning_level,
      dangerLevel: station.danger_level,
    };
  });

  return stations;
};

export const fetchWaterLevelData = async (): Promise<{
  stations: StationData[];
  timestamp: string;
}> => {
  const allStations = await fetchAllWaterLevelDataDirect();
  const timestamp = new Date().toLocaleString();

  return {
    stations: allStations,
    timestamp,
  };
};

export interface StatusCount {
  "BELOW WARNING LEVEL"?: number;
  "WARNING LEVEL"?: number;
  "DANGER LEVEL"?: number;
}

export interface StatusCountResponse {
  results: Record<string, unknown>[];
  status_count: StatusCount;
  last_updated: string;
}

export interface WeatherData {
  temperature: string;
  condition: string;
  icon?: string;
  location?: string;
}

export const fetchWeatherData = async (): Promise<WeatherData> => {
  try {
    const apiKey =
      import.meta.env.VITE_WEATHERAPI_KEY ||
      import.meta.env.VITE_OPENWEATHERMAP_KEY ||
      "********************************";
    const location = "Kathmandu";

    if (!apiKey) {
      throw new Error("API key not configured");
    }

    const response = await fetch(
      `https://api.openweathermap.org/data/2.5/weather?units=metric&q=${encodeURIComponent(location)}&appid=${apiKey}`,
    );

    if (response.status === 404) {
      throw new Error(`Location not found: ${location}`);
    }

    if (!response.ok) {
      throw new Error(`Failed to fetch weather data: ${response.statusText}`);
    }

    const data = await response.json();

    let iconName = "clear";
    const weatherMain = data.weather[0].main;

    switch (weatherMain) {
      case "Clouds":
        iconName = "clouds";
        break;
      case "Clear":
        iconName = "clear";
        break;
      case "Rain":
        iconName = "rain";
        break;
      case "Drizzle":
        iconName = "drizzle";
        break;
      case "Thunderstorm":
        iconName = "rain";
        break;
      case "Snow":
        iconName = "clouds";
        break;
      case "Mist":
      case "Smoke":
      case "Haze":
      case "Dust":
      case "Fog":
      case "Sand":
      case "Ash":
      case "Squall":
      case "Tornado":
        iconName = "mist";
        break;
      default:
        iconName = "clear";
    }

    return {
      temperature: Math.round(data.main.temp).toString(),
      condition: data.weather[0].main,
      icon: iconName,
      location: `${data.name}, ${data.sys.country}`,
    };
  } catch {
    return {
      temperature: "27",
      condition: "Partly Cloudy",
      icon: "clouds",
      location: "Kathmandu, NP",
    };
  }
};

export const fetchRainfallStatusCount =
  async (): Promise<StatusCountResponse> => {
    const response = await axiosInstance.get(
      "/api/v1/rainfall-stations/?page_size=1",
    );

    return {
      results: response.data.results || [],
      status_count: response.data.status_count || {},
      last_updated: response.data.last_updated || new Date().toISOString(),
    };
  };

export const fetchWaterLevelStatusCount =
  async (): Promise<StatusCountResponse> => {
    const response = await axiosInstance.get(
      "/api/v1/waterlevel-stations/?page_size=1",
    );

    return {
      results: response.data.results || [],
      status_count: response.data.status_count || {},
      last_updated: response.data.last_updated || new Date().toISOString(),
    };
  };

export const paginatedDataKeys = {
  all: ["paginatedData"] as const,
  rainfall: (params: PaginationParams) =>
    [...paginatedDataKeys.all, "rainfall", params] as const,
  waterLevel: (params: PaginationParams) =>
    [...paginatedDataKeys.all, "waterLevel", params] as const,
} as const;

export const useRainfallDataPaginated = (params: PaginationParams) => {
  return useQuery({
    queryKey: paginatedDataKeys.rainfall(params),
    queryFn: () => fetchRainfallDataPaginated(params),

    staleTime: 5 * 60 * 1000,

    refetchInterval: 10 * 60 * 1000,
    refetchOnWindowFocus: true,

    placeholderData: (previousData) => previousData,
  });
};

export const useWaterLevelDataPaginated = (params: PaginationParams) => {
  return useQuery({
    queryKey: paginatedDataKeys.waterLevel(params),
    queryFn: () => fetchWaterLevelDataPaginated(params),

    staleTime: 5 * 60 * 1000,

    refetchInterval: 10 * 60 * 1000,
    refetchOnWindowFocus: true,

    placeholderData: (previousData) => previousData,
  });
};

export interface MapMarkerData {
  id: number;
  station_id: string;
  station_name: string;
  latitude: number;
  longitude: number;
  status: string;
  district?: string;
  basin?: string;
}

export interface DetailedStationData {
  id: number;
  stationId: string;
  stationName: string;
  district: string;
  basin: string;
  latitude: number;
  longitude: number;
  status: string;
  lastUpdated: string;

  rainfall1h?: number;
  rainfall3h?: number;
  rainfall6h?: number;
  rainfall12h?: number;
  rainfall24h?: number;

  waterLevel?: number;
  warningLevel?: number;
  dangerLevel?: number;
  levelUpdated?: string;
  stationIndex?: string;
}

export const fetchRainfallMapData = async (): Promise<MapMarkerData[]> => {
  const response = await axiosInstance.get("/api/v1/rainfall-map/");

  const mapData: MapMarkerData[] = response.data.map(
    (station: Record<string, unknown>) => {
      const latitude = station.lat || station.latitude || 0;
      const longitude = station.lon || station.longitude || 0;

      return {
        id: station.id,
        station_id: station.station_id || station.id?.toString() || "",
        station_name:
          station.station_name || station.name || `Station ${station.id}`,
        latitude,
        longitude,
        status: station.status || "BELOW WARNING LEVEL",
        district: station.district || "",
        basin: station.basin || "",
      };
    },
  );

  return mapData;
};

export const fetchWaterLevelMapData = async (): Promise<MapMarkerData[]> => {
  try {
    let response;
    try {
      response = await axiosInstance.get("/api/v1/waterlevel-map/");
    } catch (mapError) {
      console.log("Map endpoint failed, trying stations endpoint:", mapError);

      response = await axiosInstance.get(
        "/api/v1/waterlevel-stations/?page_size=50",
      );

      response.data = response.data.results || response.data;
    }

    const mapData: MapMarkerData[] = (response.data || []).map(
      (station: Record<string, unknown>) => {
        const latitude = station.lat || station.latitude || 0;
        const longitude = station.lon || station.longitude || 0;

        return {
          id: station.id,
          station_id: station.station_id || station.id?.toString() || "",
          station_name:
            station.station_name || station.name || `Station ${station.id}`,
          latitude,
          longitude,
          status: station.status || "BELOW WARNING LEVEL",
          district: station.district || "",
          basin: station.basin || "",
        };
      },
    );
    return mapData;
  } catch (error) {
    console.error("Error fetching water level map data:", error);
    throw error;
  }
};

export const fetchRainfallStationDetail = async (
  id: number,
): Promise<DetailedStationData> => {
  const response = await axiosInstance.get(`/api/v1/rainfall-stations/${id}/`);
  const station = response.data;

  const latitude = station.latitude || station.lat || 0;
  const longitude = station.longitude || station.lon || 0;

  return {
    id: station.id,
    stationId: station.station_id || station.id?.toString() || "",
    stationName:
      station.station_name || station.name || `Station ${station.id}`,
    district: station.district || "",
    basin: station.basin || "",
    latitude,
    longitude,
    status: station.status || "BELOW WARNING LEVEL",
    lastUpdated: station.last_updated || new Date().toISOString(),

    rainfall1h: station.rainfall_1h || 0,
    rainfall3h: station.rainfall_3h || 0,
    rainfall6h: station.rainfall_6h || 0,
    rainfall12h: station.rainfall_12h || 0,
    rainfall24h: station.rainfall_24h || 0,
  };
};

export const fetchWaterLevelStationDetail = async (
  id: number,
): Promise<DetailedStationData> => {
  const response = await axiosInstance.get(
    `/api/v1/waterlevel-stations/${id}/`,
  );
  const station = response.data;

  const latitude = station.latitude || station.lat || 0;
  const longitude = station.longitude || station.lon || 0;

  return {
    id: station.id,
    stationId: station.station_id || station.id?.toString() || "",
    stationName:
      station.station_name || station.name || `Station ${station.id}`,
    district: station.district || "",
    basin: station.basin || "",
    latitude,
    longitude,
    status: station.status || "BELOW WARNING LEVEL",
    lastUpdated: station.last_updated || new Date().toISOString(),

    waterLevel: station.water_level || 0,
    warningLevel: station.warning_level || 0,
    dangerLevel: station.danger_level || 0,
    levelUpdated:
      station.level_updated || station.last_updated || new Date().toISOString(),
    stationIndex: station.station_index || "",
  };
};

export const mapDataKeys = {
  all: ["mapData"] as const,
  rainfallMap: () => [...mapDataKeys.all, "rainfallMap"] as const,
  waterLevelMap: () => [...mapDataKeys.all, "waterLevelMap"] as const,
  rainfallStation: (id: number) =>
    [...mapDataKeys.all, "rainfallStation", id] as const,
  waterLevelStation: (id: number) =>
    [...mapDataKeys.all, "waterLevelStation", id] as const,
} as const;

export const useRainfallMapData = () => {
  return useQuery({
    queryKey: mapDataKeys.rainfallMap(),
    queryFn: fetchRainfallMapData,
    staleTime: 5 * 60 * 1000,
    refetchInterval: 15 * 60 * 1000,
    refetchOnWindowFocus: true,
  });
};

export const useWaterLevelMapData = () => {
  return useQuery({
    queryKey: mapDataKeys.waterLevelMap(),
    queryFn: fetchWaterLevelMapData,
    staleTime: 5 * 60 * 1000,
    refetchInterval: 15 * 60 * 1000,
    refetchOnWindowFocus: true,
  });
};

export const useRainfallStationDetail = (
  id: number,
  enabled: boolean = false,
) => {
  return useQuery({
    queryKey: mapDataKeys.rainfallStation(id),
    queryFn: () => fetchRainfallStationDetail(id),
    enabled,
    staleTime: 2 * 60 * 1000,
  });
};

export const useWaterLevelStationDetail = (
  id: number,
  enabled: boolean = false,
) => {
  return useQuery({
    queryKey: mapDataKeys.waterLevelStation(id),
    queryFn: () => fetchWaterLevelStationDetail(id),
    enabled,
    staleTime: 2 * 60 * 1000,
  });
};

export interface SearchResult {
  id: number;
  station_name: string;
  name?: string;
  basin: string;
  district?: string;
  stationIndex?: string;
  longitude?: number;
  latitude?: number;
  status?: string;
}

export const searchRainfallStations = async (
  query: string,
): Promise<SearchResult[]> => {
  if (!query.trim()) return [];

  try {
    const response = await axiosInstance.get(`/api/v1/search/rainfall/`, {
      params: { query: query.trim() },
    });
    return response.data || [];
  } catch (error) {
    console.error("Error searching rainfall stations:", error);
    return [];
  }
};

export const searchWaterLevelStations = async (
  query: string,
): Promise<SearchResult[]> => {
  if (!query.trim()) return [];

  try {
    const response = await axiosInstance.get(`/api/v1/search/waterlevel/`, {
      params: { query: query.trim() },
    });
    return response.data || [];
  } catch (error) {
    console.error("Error searching water level stations:", error);
    return [];
  }
};

export const searchAllStations = async (
  query: string,
): Promise<{
  rainfall: SearchResult[];
  waterLevel: SearchResult[];
}> => {
  if (!query.trim()) return { rainfall: [], waterLevel: [] };

  try {
    const [rainfallResults, waterLevelResults] = await Promise.all([
      searchRainfallStations(query),
      searchWaterLevelStations(query),
    ]);

    return {
      rainfall: rainfallResults,
      waterLevel: waterLevelResults,
    };
  } catch (error) {
    console.error("Error searching stations:", error);
    return { rainfall: [], waterLevel: [] };
  }
};

export const useSearchRainfallStations = (
  query: string,
  enabled: boolean = true,
) => {
  return useQuery({
    queryKey: ["search", "rainfall", query],
    queryFn: () => searchRainfallStations(query),
    enabled: enabled && query.trim().length > 0,
    staleTime: 30 * 1000,
    gcTime: 5 * 60 * 1000,
  });
};

export const useSearchWaterLevelStations = (
  query: string,
  enabled: boolean = true,
) => {
  return useQuery({
    queryKey: ["search", "waterLevel", query],
    queryFn: () => searchWaterLevelStations(query),
    enabled: enabled && query.trim().length > 0,
    staleTime: 30 * 1000,
    gcTime: 5 * 60 * 1000,
  });
};

export const useSearchAllStations = (
  query: string,
  enabled: boolean = true,
) => {
  return useQuery({
    queryKey: ["search", "all", query],
    queryFn: () => searchAllStations(query),
    enabled: enabled && query.trim().length >= 2,
    staleTime: 30 * 1000,
    gcTime: 5 * 60 * 1000,
  });
};
