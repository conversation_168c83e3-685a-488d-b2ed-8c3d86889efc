import { useMutation } from "@tanstack/react-query";
import { API_ROUTES } from "@/config/api";
import { axiosInstance } from "@/lib/axios";
import { Media, MediaRequest } from "@/types/media";


export const useUploadMedia = () => {
  return useMutation({
    mutationFn: async (request: MediaRequest): Promise<Media> => {
      const formData = new FormData();
      formData.append("file", request.file);
      formData.append("media_type", request.media_type);
      formData.append("title", request.title);

      if (request.description) {
        formData.append("description", request.description);
      }

      if (request.is_public !== undefined) {
        formData.append("is_public", String(request.is_public));
      }

      if (request.owner !== undefined) {
        formData.append("owner", String(request.owner));
      }

      const { data } = await axiosInstance.post(
        API_ROUTES.media.upload,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );
      return data;
    },
  });
};
