import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { axiosInstance } from "@/lib/axios";
import { API_ROUTES } from "@/config/api";
import { PaginatedResponse, PaginationParams, buildPaginationQuery } from "@/lib/utils";
import type {
  SendAlert,
  SendAlertRequest,
  PatchedSendAlertRequest,
} from "@/types/sendAlert";


export const sendAlertKeys = {
  all: ["sendAlerts"] as const,
  lists: () => [...sendAlertKeys.all, "list"] as const,
  detail: (id: number) => [...sendAlertKeys.all, "detail", id] as const,
};


export const useSendAlertsPaginated = (params?: PaginationParams) => {
  return useQuery({
    queryKey: [...sendAlertKeys.lists(), params],
    queryFn: async () => {
      const queryString = buildPaginationQuery(params || {});
      const { data } = await axiosInstance.get<PaginatedResponse<SendAlert>>(
        `${API_ROUTES.sendAlert.list}${queryString}`,
      );
      return data;
    },
  });
};


export const useSendAlerts = () => {
  return useQuery({
    queryKey: sendAlertKeys.lists(),
    queryFn: async () => {

      const { fetchAllPages } = await import("@/lib/utils");

      const fetchSendAlertsPage = async (params: PaginationParams): Promise<PaginatedResponse<SendAlert>> => {
        const queryString = buildPaginationQuery(params);
        const { data } = await axiosInstance.get<PaginatedResponse<SendAlert>>(
          `${API_ROUTES.sendAlert.list}${queryString}`,
        );
        return data;
      };

      const allAlerts = await fetchAllPages(fetchSendAlertsPage, 50);
      return allAlerts;
    },
  });
};


export const useSendAlert = (id: number) => {
  return useQuery({
    queryKey: sendAlertKeys.detail(id),
    queryFn: async (): Promise<SendAlert> => {
      const { data } = await axiosInstance.get(
        API_ROUTES.sendAlert.getById(id),
      );
      return data;
    },
    enabled: !!id,
  });
};


export const useCreateSendAlert = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (alertData: SendAlertRequest): Promise<SendAlert> => {
      const formData = new FormData();


      Object.entries(alertData).forEach(([key, value]) => {
        if (key !== "file_upload" && value !== undefined) {
          formData.append(key, String(value));
        }
      });


      if (alertData.file_upload) {
        formData.append("file_upload", alertData.file_upload);
      }

      const { data } = await axiosInstance.post(
        API_ROUTES.sendAlert.create,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );
      return data;
    },
    onSuccess: () => {

      queryClient.invalidateQueries({ queryKey: sendAlertKeys.lists() });
    },
  });
};


export const useUpdateSendAlert = (id: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (alert: PatchedSendAlertRequest): Promise<SendAlert> => {
      const formData = new FormData();


      Object.entries(alert).forEach(([key, value]) => {
        if (key !== "file_upload" && value !== undefined) {
          formData.append(key, value as string);
        }
      });


      if (alert.file_upload) {
        formData.append("file_upload", alert.file_upload);
      }

      const { data } = await axiosInstance.patch(
        API_ROUTES.sendAlert.update(id),
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: sendAlertKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: sendAlertKeys.lists() });
    },
  });
};


export const useDeleteSendAlert = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await axiosInstance.delete(API_ROUTES.sendAlert.delete(id));
    },
    onSuccess: () => {

      queryClient.invalidateQueries({ queryKey: sendAlertKeys.lists() });
    },
  });
};
