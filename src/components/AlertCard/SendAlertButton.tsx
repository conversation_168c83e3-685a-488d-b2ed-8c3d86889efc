import React from "react";
import { useNavigate } from "react-router-dom";
import { AlertTemplate } from "@/types/alertTemplate";

interface SendAlertButtonProps {
  templateData: AlertTemplate;
  className?: string;
}

export const SendAlertButton: React.FC<SendAlertButtonProps> = ({
  templateData,
  className = "",
}) => {
  const navigate = useNavigate();

  const handleSendAlert = () => {

    navigate("/risk/send-alert/manual", {
      state: {
        templateData: {
          id: templateData.id,
          template_name: templateData.template_name,
          alert_message: templateData.alert_message,
          mail_subject: templateData.mail_subject,
          hazard_type: templateData.hazard_type,
          template_type: templateData.template_type,
          severity: templateData.severity,
        },
        autoFill: true,
      },
    });
  };

  return (
    <button
      className={`flex h-9 w-9 cursor-pointer items-center justify-center rounded-full hover:bg-[#e9f3f0] ${className}`}
      onClick={handleSendAlert}
      title="Send Alert"
    >
      <img src="/send.svg" alt="Send" />
    </button>
  );
};
