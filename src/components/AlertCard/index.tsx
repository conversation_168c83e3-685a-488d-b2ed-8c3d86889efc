import React, { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";
import { useDeleteAlertTemplate } from "@/api/alertTemplates";
import { toast } from "sonner";
import TemplateForm from "@/components/Template/alertTemplate";
import { AlertTemplate } from "@/types/alertTemplate";
import { SendAlertButton } from "./SendAlertButton";

interface AlertCardProps {
  id: string;
  title: string;
  description: string;
  type: string;
  severity: "High Alert" | "Medium Alert" | "Low Alert";
  isLast?: boolean;
  icon: string;
  templateData: AlertTemplate;
  onDelete?: () => void;
  onEdit?: () => void;
}

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case "High Alert":
      return "text-red-600";
    case "Medium Alert":
      return "text-[#FFAE19]";
    case "Low Alert":
      return "text-green-600";
    default:
      return "text-gray-700";
  }
};

export const AlertCard: React.FC<AlertCardProps> = ({
  id,
  title,
  description,
  type,
  severity,
  icon,
  isLast = false,
  templateData,
  onDelete,
  onEdit,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const descriptionRef = useRef<HTMLParagraphElement>(null);

  const deleteTemplate = useDeleteAlertTemplate();

  useEffect(() => {
    const checkOverflow = () => {
      const element = descriptionRef.current;
      if (element) {
        setIsOverflowing(element.scrollHeight > element.clientHeight);
      }
    };

    checkOverflow();

    window.addEventListener("resize", checkOverflow);
    return () => window.removeEventListener("resize", checkOverflow);
  }, [description]);

  const handleDelete = async () => {
    try {
      await deleteTemplate.mutateAsync(parseInt(id));
      toast.success("Template deleted successfully");
      setShowDeleteDialog(false);

      if (onDelete) {
        onDelete();
      }
    } catch {
      toast.error("Failed to delete template");
    }
  };

  const handleEditSuccess = () => {
    if (onEdit) {
      onEdit();
    }
  };

  return (
    <>
      <div className={isLast ? "flex-1 bg-white pb-30" : ""}>
        <Card className={"gap-2 border-neutral-100 p-4"}>
          <CardContent className="flex items-center gap-12 p-0">
            <div className="flex flex-1 items-center gap-6">
              <Button
                variant="ghost"
                className="flex h-10 w-10 items-center justify-center gap-2.5 overflow-hidden rounded-[40px] bg-[#f4f7fe] p-0"
                aria-label={type}
              >
                <img
                  src={icon}
                  alt={type}
                  className="h-full w-full object-contain"
                />
              </Button>

              <div className="flex flex-1 flex-col gap-2 pb-3">
                <h3 className="font-poppins text-[14px] leading-[20px] font-semibold text-[#0F172A]">
                  {title}
                </h3>
                <div className="relative">
                  <p
                    ref={descriptionRef}
                    className={`font-poppins text-[14px] leading-[20px] font-normal text-[#0F172A] ${
                      isExpanded
                        ? ""
                        : "line-clamp-1 overflow-hidden text-ellipsis"
                    }`}
                  >
                    {description}
                  </p>
                  {(isOverflowing || isExpanded) && (
                    <Button
                      variant="ghost"
                      className="h-auto cursor-pointer p-0 text-[#02475c]"
                      onClick={() => setIsExpanded(!isExpanded)}
                    >
                      {isExpanded ? "See Less" : "See More"}
                    </Button>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex min-w-[30px] rounded-lg border border-[#e9e9e9] px-2.5 py-2">
                <span className="font-poppins text-sm font-semibold text-gray-700">
                  {type}
                </span>
              </div>
              <div className="flex min-w-[120px] items-center justify-center rounded-lg border border-[#e9e9e9] px-3 py-2">
                <span
                  className={`font-poppins text-sm font-semibold ${getSeverityColor(severity)}`}
                >
                  {severity}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-1">
              <TemplateForm
                editData={templateData}
                onSuccess={handleEditSuccess}
                trigger={
                  <button className="flex h-9 w-9 cursor-pointer items-center justify-center rounded-full hover:bg-[#e9f3f0]">
                    <img src="/edit.svg" alt="Edit" />
                  </button>
                }
              />
              <button
                className="flex h-9 w-9 cursor-pointer items-center justify-center rounded-full hover:bg-[#e9f3f0]"
                onClick={() => setShowDeleteDialog(true)}
              >
                <img src="/delete.svg" alt="Delete" />
              </button>
              <SendAlertButton templateData={templateData} />
            </div>
          </CardContent>
        </Card>

        <div className="mx-0 h-px w-full bg-[#d9d9d9]" />
      </div>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="rounded-lg p-6 sm:max-w-[425px]">
          <DialogHeader className="mb-6">
            <DialogTitle className="Lato-semibold text-[16px] font-semibold text-[#262626]">
              Delete Template
            </DialogTitle>
            <DialogDescription className="Lato-regular mt-4 text-[14px] text-[#667085]">
              Are you sure want to delete "{title}" Template ? Once deleted the
              action is undone
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-end gap-[8px]">
            <Button
              type="button"
              onClick={handleDelete}
              className="flex h-[32px] w-[90px] items-center justify-center rounded-[8px] border border-[#02475C] bg-white px-[24px] py-[8px] text-sm text-[#02475C] hover:bg-gray-50"
              disabled={deleteTemplate.isPending}
            >
              {deleteTemplate.isPending ? (
                <>
                  <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                  <span className="text-xs">Deleting...</span>
                </>
              ) : (
                "Delete"
              )}
            </Button>
            <Button
              type="button"
              onClick={() => setShowDeleteDialog(false)}
              className="flex h-[32px] w-[90px] items-center justify-center rounded-[8px] border-none bg-[linear-gradient(294.02deg,#02475C_0%,#1D8E89_100%)] px-[24px] py-[8px] text-sm text-white hover:opacity-90"
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
