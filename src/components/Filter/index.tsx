import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { CalendarIcon, RotateCcw } from "lucide-react";
import React, { useState, useEffect } from "react";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export interface FilterValues {
  hazardType?: string;
  communicationType?: string;
  severity?: string;
  startDate?: Date | null;
  endDate?: Date | null;
}

interface FilterProps {
  onClose?: () => void;
  onApplyFilter: (filters: FilterValues) => void;
  initialValues?: FilterValues;
  showCommunicationFilter?: boolean;
}

export const Filter: React.FC<FilterProps> = ({
  onClose,
  onApplyFilter,
  initialValues = {},
  showCommunicationFilter = true,
}) => {

  const [windowHeight, setWindowHeight] = useState<number>(window.innerHeight);


  useEffect(() => {
    const handleResize = () => {
      setWindowHeight(window.innerHeight);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const [hazardType, setHazardType] = useState<string | undefined>(
    initialValues.hazardType,
  );
  const [communicationType, setCommunicationType] = useState<
    string | undefined
  >(initialValues.communicationType || "sms");
  const [severity, setSeverity] = useState<string | undefined>(
    initialValues.severity,
  );
  const [startDate, setStartDate] = useState<Date | null | undefined>(
    initialValues.startDate,
  );
  const [endDate, setEndDate] = useState<Date | null | undefined>(
    initialValues.endDate,
  );


  const communicationOptions = [
    { value: "sms", label: "SMS" },
    { value: "email", label: "Email" },
  ];


  const handleReset = () => {
    setHazardType(undefined);
    setCommunicationType(showCommunicationFilter ? "sms" : undefined);
    setSeverity(undefined);
    setStartDate(null);
    setEndDate(null);
  };


  const handleApply = () => {
    onApplyFilter({
      hazardType,
      communicationType: showCommunicationFilter
        ? communicationType
        : undefined,
      severity,
      startDate,
      endDate,
    });
    if (onClose) onClose();
  };

  return (
    <div className="absolute top-15 right-0 z-50">
      <Card 
        className={`h-[710px] w-[380px] rounded-lg border border-[#d0d5dd] bg-white/80 p-6 shadow-lg backdrop-blur-md overflow-y-auto flex flex-col min-h-[300px] ${windowHeight < 800 ? 'max-h-[90vh]' : ''}`}
        style={{ 
          maxHeight: windowHeight < 800 ? `${windowHeight * 0.9}px` : '710px',
          width: '380px',
          maxWidth: '100%'
        }}
      >
        <div className="flex flex-col gap-8 h-full">
          <div className="flex flex-col gap-2">
            <h4 className="font-heading-h4-20px text-xl font-semibold text-slate-900">
              Filter Template
            </h4>
          </div>

          <div className="flex flex-1 flex-col gap-5 overflow-y-auto">
            <div className="space-y-5">

              <div className="space-y-1">
                <Label className="text-blackblack-700 font-body-body-md-14">
                  Hazard Type
                </Label>
                <Select value={hazardType} onValueChange={setHazardType}>
                  <SelectTrigger className="poppins-regular relative h-10 w-full rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                    <SelectValue
                      placeholder="Choose Hazard Type"
                      className="text-sm font-semibold text-[#9d9d9d]"
                    />
                    <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555]">
                      arrow_drop_down
                    </span>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fire">Fire</SelectItem>
                    <SelectItem value="flood">Flood</SelectItem>
                    <SelectItem value="drought">Drought</SelectItem>
                    <SelectItem value="landslide">Landslide</SelectItem>
                  </SelectContent>
                </Select>
              </div>


              {showCommunicationFilter && (
                <div className="space-y-1">
                  <Label className="font-body-body-md-14 my-1 flex items-center text-[#434343]">
                    Communication Channel
                  </Label>

                  <ToggleGroup
                    type="single"
                    value={communicationType}
                    onValueChange={(value) => {
                      if (value) setCommunicationType(value);
                    }}
                    className="inline-flex items-center rounded bg-[#F5F5F5] p-0.5 h-9 w-[109px]"
                  >
                    {communicationOptions.map((option) => (
                      <ToggleGroupItem
                        key={option.value}
                        value={option.value}
                        className={`flex-1 h-6 rounded-sm border-1 border-solid border-[#d0d5dd] px-2 py-0 text-xs data-[state=off]:border-none data-[state=off]:text-[#757575] data-[state=on]:border-[#02475C] data-[state=on]:bg-[#E9F3F0] data-[state=on]:text-[#02475C]`}
                      >
                        <span className="font-[600] whitespace-nowrap text-xs">
                          {option.label}
                        </span>
                      </ToggleGroupItem>
                    ))}
                  </ToggleGroup>
                </div>
              )}


              <div className="space-y-1">
                <Label className="text-blackblack-700 font-body-body-md-14">
                  Severity
                </Label>
                <Select value={severity} onValueChange={setSeverity}>
                  <SelectTrigger className="poppins-regular relative h-10 w-full rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                    <SelectValue
                      placeholder="Choose Severity"
                      className="text-sm font-semibold text-[#9d9d9d]"
                    />
                    <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555]">
                      arrow_drop_down
                    </span>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High Alert</SelectItem>
                    <SelectItem value="medium">Medium Alert</SelectItem>
                    <SelectItem value="low">Low Alert</SelectItem>
                  </SelectContent>
                </Select>
              </div>


              <div className="space-y-1">
                <Label className="text-blackblack-700 font-body-body-md-14">
                  Date
                </Label>
                <div className="flex items-end gap-4">
                  <div className="flex-1">
                    <Popover>
                      <PopoverTrigger asChild>
                        <div className="relative">
                          <Input
                            placeholder="From"
                            className="bg-white-900 text-greygray-400 h-10 border-[#d0d5dd] pr-10 pl-3"
                            value={startDate ? format(startDate, "PP") : ""}
                            readOnly
                          />
                          <CalendarIcon className="text-blackblack-600 absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 transform" />
                        </div>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={startDate || undefined}
                          onSelect={setStartDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="flex-1">
                    <Popover>
                      <PopoverTrigger asChild>
                        <div className="relative">
                          <Input
                            placeholder="To"
                            className="bg-white-900 text-greygray-400 h-10 border-[#d0d5dd] pr-10 pl-3"
                            value={endDate ? format(endDate, "PP") : ""}
                            readOnly
                          />
                          <CalendarIcon className="text-blackblack-600 absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 transform" />
                        </div>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={endDate || undefined}
                          onSelect={setEndDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>
            </div>
          </div>


          <div className="mt-auto pt-4 flex w-full items-center justify-center gap-2">
            <Button
              variant="ghost"
              className="text-primary-1 font-button-button-14 flex h-10 items-center gap-2 px-2 py-3"
              onClick={handleReset}
            >
              <RotateCcw className="h-5 w-5" />
              Reset Filter
            </Button>
            <Button
              className="font-button-button-14 h-10 bg-[linear-gradient(347deg,rgba(2,71,92,1)_0%,rgba(29,142,137,1)_100%)] px-6 py-3 text-white"
              onClick={handleApply}
            >
              Apply Filter
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};
