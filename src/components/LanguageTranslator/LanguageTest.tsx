import React from "react";
import { useLanguage } from "@/hooks/useLanguage";

const LanguageTest: React.FC = () => {
  const { t, changeLanguage, currentLanguage, isNepali, isEnglish } =
    useLanguage();

  return (
    <div className="max-w-md rounded-lg bg-white p-4 shadow-md">
      <h2 className="mb-4 text-lg font-bold">Language Test Component</h2>

      <div className="mb-4">
        <p>
          <strong>Current Language:</strong> {currentLanguage}
        </p>
        <p>
          <strong>Is Nepali:</strong> {isNepali() ? "Yes" : "No"}
        </p>
        <p>
          <strong>Is English:</strong> {isEnglish() ? "Yes" : "No"}
        </p>
      </div>

      <div className="mb-4">
        <h3 className="mb-2 font-semibold">Sample Translations:</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="mb-1 font-medium">Dashboard:</h4>
            <ul className="space-y-1">
              <li>
                <strong>Warning:</strong> {t("warning")}
              </li>
              <li>
                <strong>Danger:</strong> {t("danger")}
              </li>
              <li>
                <strong>River Watch:</strong> {t("riverWatch")}
              </li>
              <li>
                <strong>Show All:</strong> {t("showAll")}
              </li>
            </ul>
          </div>
          <div>
            <h4 className="mb-1 font-medium">Time Periods:</h4>
            <ul className="space-y-1">
              <li>
                <strong>1 Hour:</strong> {t("1hr")}
              </li>
              <li>
                <strong>3 Hours:</strong> {t("3hr")}
              </li>
              <li>
                <strong>6 Hours:</strong> {t("6hr")}
              </li>
              <li>
                <strong>24 Hours:</strong> {t("24hr")}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex gap-2">
        <button
          onClick={() => changeLanguage("en")}
          className={`rounded px-3 py-1 text-sm ${
            currentLanguage === "en"
              ? "bg-blue-500 text-white"
              : "bg-gray-200 text-gray-700"
          }`}
        >
          English
        </button>
        <button
          onClick={() => changeLanguage("ne")}
          className={`rounded px-3 py-1 text-sm ${
            currentLanguage === "ne"
              ? "bg-blue-500 text-white"
              : "bg-gray-200 text-gray-700"
          }`}
        >
          नेपाली
        </button>
      </div>
    </div>
  );
};

export default LanguageTest;
