import React from "react";
import { useLanguage } from "@/hooks/useLanguage";

interface LanguageTranslatorProps {
  className?: string;
}

const LanguageTranslator: React.FC<LanguageTranslatorProps> = ({
  className = "",
}) => {
  const { changeLanguage, currentLanguage } = useLanguage();

  const handleLanguageChange = (
    event: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    const selectedLanguage = event.target.value;
    changeLanguage(selectedLanguage);
  };

  return (
    <div
      className={`flex items-center gap-1 rounded-md border border-gray-200 bg-white px-2 py-1 shadow-sm ${className}`}
    >
      <svg
        width="12"
        height="12"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="text-gray-600"
      >
        <path
          d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"
          fill="currentColor"
        />
      </svg>
      <select
        className="cursor-pointer border-none bg-transparent text-xs text-gray-700 outline-none"
        value={currentLanguage}
        onChange={handleLanguageChange}
      >
        <option value="en">EN</option>
        <option value="ne">नेपा</option>
      </select>
    </div>
  );
};

export default LanguageTranslator;
