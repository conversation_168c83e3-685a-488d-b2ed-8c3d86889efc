import { useState } from "react";
import { useLogin } from "@/api/auth";
import { toast } from "sonner";
import * as Dialog from "@radix-ui/react-dialog";
import { Eye, EyeOff, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";

interface LoginDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function LoginDialog({ open, onOpenChange }: LoginDialogProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const navigate = useNavigate();

  const login = useLogin();
  const { isAuthenticated, login: authLogin } = useAuth();

  if (isAuthenticated && open) {
    onOpenChange(false);
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username || !password) {
      toast.error("Please enter both username and password");
      return;
    }

    setIsLoggingIn(true);

    try {
      const response = await login.mutateAsync({
        username,
        password,
      });

      authLogin(response);

      setUsername("");
      setPassword("");

      toast.success("Logged in successfully");
      onOpenChange(false);

      navigate("/risk/dashboard");
    } catch (error: unknown) {
      interface ApiError {
        response?: {
          status?: number;
          data?: {
            message?: string;
          };
        };
        message?: string;
      }

      const apiError = error as ApiError;

      if (apiError.response?.status === 401) {
        toast.error("Incorrect username or password. Please try again.");
      } else if (apiError.response?.data?.message) {
        toast.error(apiError.response.data.message);
      } else if (apiError.message) {
        toast.error(apiError.message);
      } else {
        toast.error("Login failed. Please try again later.");
      }
    } finally {
      setIsLoggingIn(false);
    }
  };

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/30 backdrop-blur-sm" />
        <Dialog.Content className="fixed top-[50%] left-[50%] z-50 translate-x-[-50%] translate-y-[-50%]">
          <Dialog.Title className="sr-only">Sign In</Dialog.Title>
          <Dialog.Description className="sr-only">
            Enter your username and password to login
          </Dialog.Description>
          <Card className="w-[440px] rounded-xl border border-solid border-slate-200 bg-white p-10 shadow-lg">
            <div className="mb-10 flex gap-4">
              <img
                src="/PrepareLogo.svg"
                alt="Prepare Logo"
                className="h-[30px] w-[90px]"
              />
            </div>
            <form onSubmit={handleSubmit} className="flex flex-col gap-12">
              <div className="flex flex-col gap-1">
                <h2 className="poppins-semibold text-xl text-[#434343]">
                  Sign In
                </h2>
                <p className="poppins-regular text-sm text-[#9d9d9d]">
                  Enter your username and password to login
                </p>
              </div>

              <div className="mt-[-15px] flex flex-col gap-10">
                <div className="flex flex-col gap-5">
                  <div className="flex flex-col gap-1">
                    <Label
                      htmlFor="username"
                      className="poppins-medium text-sm text-[#434343]"
                    >
                      Username
                    </Label>
                    <Input
                      id="username"
                      type="text"
                      placeholder="Enter Username"
                      className="h-10 rounded-lg border border-solid border-[#d0d5dd] bg-white p-3 text-sm text-[#000000]"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      disabled={isLoggingIn}
                    />
                  </div>

                  <div className="flex flex-col gap-1">
                    <Label
                      htmlFor="password"
                      className="poppins-medium text-sm text-[#000000]"
                    >
                      Password
                    </Label>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter Password"
                        className="h-10 rounded-lg border border-solid border-[#d0d5dd] bg-white p-3 pr-10 text-sm text-[#000000]"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        disabled={isLoggingIn}
                      />
                      <button
                        type="button"
                        onClick={togglePasswordVisibility}
                        className="absolute top-[60%] right-3 -translate-y-1/2 text-[#555555]"
                        disabled={isLoggingIn}
                        aria-label={
                          showPassword ? "Hide password" : "Show password"
                        }
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-500" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-500" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                <Button
                  type="submit"
                  disabled={isLoggingIn}
                  className="poppins-medium w-full bg-[linear-gradient(294.02deg,#02475C_0%,#1D8E89_100%)] hover:opacity-90"
                >
                  {isLoggingIn ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Logging in...
                    </>
                  ) : (
                    "Login"
                  )}
                </Button>
              </div>
            </form>
          </Card>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
