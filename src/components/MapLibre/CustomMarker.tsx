
import maplibregl, { Map,  Popup } from 'maplibre-gl';
import { MapMarkerData } from '@/api/mapData';
import { MARKER_CONFIG, POPUP_CONFIG } from './constants';
import { getMarkerIcon } from './utils';
import { createLoadingPopupContent } from './PopupContent';
import { MarkerState } from './types';

interface CustomMarkerProps {
  map: Map | null;
  station: MapMarkerData;
  dataType: 'rainfall' | 'waterLevel';
  markers: React.MutableRefObject<MarkerState[]>;
  currentPopup: React.MutableRefObject<Popup | null>;
  onMarkerClick: (stationId: number, stationType: 'rainfall' | 'waterLevel') => void;
}

export const createMarker = ({
  map,
  station,
  dataType,
  markers,
  currentPopup,
  onMarkerClick
}: CustomMarkerProps) => {
  if (!map || station.latitude === 0 || station.longitude === 0) return;

  try {
    const markerEl = document.createElement('div');
    markerEl.className = `custom-marker ${dataType === 'rainfall' ? 'rainfall-marker' : 'water-level-marker'}`;
    const iconUrl = getMarkerIcon(station.status, dataType);
    markerEl.style.backgroundImage = `url(${iconUrl})`;
    markerEl.style.filter = 'none';

    const popup = new maplibregl.Popup(POPUP_CONFIG);

    const marker = new maplibregl.Marker({
      element: markerEl,
      anchor: MARKER_CONFIG.anchor,
      offset: MARKER_CONFIG.offset
    })
    .setLngLat([station.longitude, station.latitude])
    .addTo(map);

    markerEl.addEventListener('click', (e) => {
      e.stopPropagation();

      if (currentPopup.current) {
        currentPopup.current.remove();
      }

      popup.setHTML(createLoadingPopupContent(station.station_name));
      popup.setLngLat([station.longitude, station.latitude]);
      popup.addTo(map);

      currentPopup.current = popup;

      onMarkerClick(station.id, dataType);
    });

    markers.current.push({
      marker,
      popup,
      stationId: station.id,
      stationType: dataType,
    });
  } catch (error) {
    console.error("Error adding marker for station:", station.station_name, error);
  }
};

export const clearMarkers = (markers: React.MutableRefObject<MarkerState[]>, currentPopup: React.MutableRefObject<Popup | null>) => {
  markers.current.forEach(({ marker, popup }) => {
    marker.remove();
    popup.remove();
  });
  markers.current = [];

  if (currentPopup.current) {
    currentPopup.current.remove();
    currentPopup.current = null;
  }
};