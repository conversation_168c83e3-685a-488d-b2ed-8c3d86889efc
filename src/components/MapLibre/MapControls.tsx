import React from "react";
import { Map } from "maplibre-gl";
import { Loader2, RotateCcw } from "lucide-react";
import { MAP_CONFIG } from "./constants";

interface GoBackButtonProps {
  show: boolean;
  map: Map | null;
  onGoBack?: () => void;
}

export const GoBackButton: React.FC<GoBackButtonProps> = ({
  show,
  map,
  onGoBack,
}) => {
  if (!show) return null;

  const handleGoBack = () => {
    if (map) {
      map.flyTo({
        center: MAP_CONFIG.center,
        zoom: MAP_CONFIG.zoom,
        duration: 1000,
      });
    }

    if (onGoBack) {
      onGoBack();
    }
  };

  return (
    <button
      onClick={handleGoBack}
      className="absolute top-4 left-4 z-[1000] flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-lg transition-all duration-200 hover:bg-gray-50 hover:text-gray-900 hover:shadow-xl"
      title="Go back to overview"
    >
      <RotateCcw className="h-4 w-4" />
      Go Back
    </button>
  );
};

interface LoadingOverlayProps {
  isLoading: boolean;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
}) => {
  if (!isLoading) return null;

  return (
    <div className="bg-opacity-75 absolute inset-0 flex items-center justify-center bg-white">
      <div className="flex items-center space-x-2">
        <Loader2 className="h-6 w-6 animate-spin text-[#02475C]" />
        <span className="text-[#02475C]">Loading map data...</span>
      </div>
    </div>
  );
};

interface ErrorDisplayProps {
  hasError: boolean;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ hasError }) => {
  if (!hasError) return null;

  return (
    <div className="flex h-full items-center justify-center bg-gray-100">
      <div className="text-center">
        <p className="mb-2 text-red-600">Error loading map data</p>
        <p className="text-sm text-gray-600">Please try refreshing the page</p>
      </div>
    </div>
  );
};
