import React from "react";
import { useTranslation } from "react-i18next";

interface MapLegendProps {
  dataType: "waterLevel" | "rainfall" | "both";
}

export const MapLegend: React.FC<MapLegendProps> = ({ dataType }) => {
  const { t } = useTranslation();

  const legendItems = {
    waterLevel: [
      { label: t("danger"), icon: "/reds.svg" },
      { label: t("warning"), icon: "/yellows.svg" },
      { label: t("belowWarning"), icon: "/greens.svg" },
    ],
    rainfall: [
      { label: t("danger"), icon: "/Danger.svg" },
      { label: t("warning"), icon: "/yellow.svg" },
      { label: t("belowWarning"), icon: "/green.svg" },
    ],
  };

  const renderLegendSection = (
    type: "waterLevel" | "rainfall",
    title: string,
  ) => (
    <div className={type === "rainfall" ? "" : "mb-1"}>
      <div className="mb-1 text-[14px] font-semibold text-[#3A3F50]">
        {title}
      </div>
      <div className="flex items-start">
        <div className="flex flex-col">
          {legendItems[type].map((item, index) => (
            <img
              key={index}
              src={item.icon}
              alt={item.label}
              className="relative h-7 w-7 border border-[#E9E9E9] bg-white p-1"
              style={{ zIndex: legendItems[type].length - index }}
            />
          ))}
        </div>

        <div className="ml-4 flex flex-col">
          {legendItems[type].map((item, index) => (
            <div
              key={index}
              className="flex h-7 items-center text-[12px] font-normal"
            >
              {item.label}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="absolute bottom-1 left-4 z-[1000] w-fit rounded-xl border-[1px] border-[#D7D7D7] bg-white p-3 font-sans text-[#3A3F50]">
      {dataType === "both" ? (
        <>
          {renderLegendSection("waterLevel", t("legendWaterLevel"))}
          {renderLegendSection("rainfall", t("legendRainfall"))}
        </>
      ) : dataType === "waterLevel" ? (
        renderLegendSection("waterLevel", t("legendWaterLevel"))
      ) : (
        renderLegendSection("rainfall", t("legendRainfall"))
      )}
    </div>
  );
};

export default MapLegend;
