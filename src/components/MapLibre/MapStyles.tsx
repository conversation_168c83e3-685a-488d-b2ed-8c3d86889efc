import { MARKER_CONFIG } from "./constants"; 


export const addStyleToDocument = (id: string, css: string) => {
  if (document.getElementById(id)) return;

  const style = document.createElement("style");
  style.id = id;
  style.textContent = css;
  document.head.appendChild(style);
};


export const getMarkerStyles = () => `
  .custom-marker {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: ${MARKER_CONFIG.width}px;
    height: ${MARKER_CONFIG.height}px;
    cursor: pointer;
  }

  .custom-marker.rainfall-marker {
    width: ${MARKER_CONFIG.width}px;
    height: ${MARKER_CONFIG.height}px;
    background-size: 16px 16px; /* Smaller size for rainfall markers */
  }

  .custom-marker.water-level-marker {
    width: ${MARKER_CONFIG.width}px;
    height: ${MARKER_CONFIG.height}px;
    background-size: contain; /* Keep original size for water level markers */
  }

  .maplibregl-marker {
    filter: none !important;
  }
`;


export const getPopupStyles = () => `
  .maplibregl-popup {
    z-index: 100;
    filter: none !important;
  }
  .maplibregl-popup-content {
    padding: 0 !important;
    border-radius: 0px !important;
    box-shadow: none !important;
    background: transparent !important;
  }
  .maplibregl-popup-tip {
    border-top-color: white !important;
  }

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
    font-family: 'Inter', sans-serif;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f4f6;
    border-top: 2px solid #02475C;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 8px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-content p {
    margin: 0;
    font-size: 12px;
    color: #6b7280;
  }
`;