import React, { useRef, useEffect, useState } from "react";
import maplibregl, { Map, Popup } from "maplibre-gl";
import "maplibre-gl/dist/maplibre-gl.css";
import {
  useRainfallMapData,
  useWaterLevelMapData,
  useRainfallStationDetail,
  useWaterLevelStationDetail,
} from "@/api/mapData";

import { MAP_CONFIG } from "./constants";
import {
  MarkerState,
  OptimizedMapViewProps,
  SelectedStationState,
} from "./types";
import {
  addStyleToDocument,
  getMarkerStyles,
  getPopupStyles,
} from "./MapStyles";
import { clearMarkers, createMarker } from "./CustomMarker";
import {
  generateRainfallPopupContent,
  generateWaterLevelPopupContent,
} from "./PopupContent";
import { ErrorDisplay, GoBackButton, LoadingOverlay } from "./MapControls";
import MapLegend from "./MapLegend";

export const OptimizedMapView: React.FC<OptimizedMapViewProps> = ({
  dataType,
  focusedStation,
  onClearFocus,
}) => {
  const showRainfall = dataType === "rainfall" || dataType === "both";
  const showWaterLevel = dataType === "waterLevel" || dataType === "both";

  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<Map | null>(null);
  const markers = useRef<MarkerState[]>([]);
  const currentPopup = useRef<Popup | null>(null);

  const [selectedStation, setSelectedStation] =
    useState<SelectedStationState | null>(null);
  const [showGoBackButton, setShowGoBackButton] = useState<boolean>(false);
  const [isInFocusMode, setIsInFocusMode] = useState<boolean>(false);
  const {
    data: rainfallMapData,
    isLoading: isRainfallLoading,
    error: rainfallError,
  } = useRainfallMapData();

  const {
    data: waterLevelMapData,
    isLoading: isWaterLevelLoading,
    error: waterLevelError,
  } = useWaterLevelMapData();

  const { data: rainfallStationDetail, isLoading: isRainfallDetailLoading } =
    useRainfallStationDetail(
      selectedStation?.id || 0,
      selectedStation?.type === "rainfall",
    );

  const {
    data: waterLevelStationDetail,
    isLoading: isWaterLevelDetailLoading,
  } = useWaterLevelStationDetail(
    selectedStation?.id || 0,
    selectedStation?.type === "waterLevel",
  );

  const isLoading = isRainfallLoading || isWaterLevelLoading;
  const hasError = Boolean(rainfallError) || Boolean(waterLevelError);
  useEffect(() => {
    if (!selectedStation) return;

    const markerState = markers.current.find(
      (m) =>
        m.stationId === selectedStation.id &&
        m.stationType === selectedStation.type,
    );

    if (!markerState) return;

    if (
      selectedStation.type === "rainfall" &&
      rainfallStationDetail &&
      !isRainfallDetailLoading
    ) {
      markerState.popup.setHTML(
        generateRainfallPopupContent(rainfallStationDetail),
      );
    } else if (
      selectedStation.type === "waterLevel" &&
      waterLevelStationDetail &&
      !isWaterLevelDetailLoading
    ) {
      markerState.popup.setHTML(
        generateWaterLevelPopupContent(waterLevelStationDetail),
      );
    }
  }, [
    rainfallStationDetail,
    waterLevelStationDetail,
    isRainfallDetailLoading,
    isWaterLevelDetailLoading,
    selectedStation,
  ]);

  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    try {
      map.current = new maplibregl.Map({
        container: mapContainer.current,
        attributionControl: false,
        style: {
          version: 8,
          sources: {
            osm: {
              type: "raster",
              tiles: [
                "https://a.tile.openstreetmap.org/{z}/{x}/{y}.png",
                "https://b.tile.openstreetmap.org/{z}/{x}/{y}.png",
                "https://c.tile.openstreetmap.org/{z}/{x}/{y}.png",
              ],
              tileSize: 256,
            },
          },
          layers: [
            {
              id: "osm-layer",
              type: "raster",
              source: "osm",
              minzoom: 0,
              maxzoom: MAP_CONFIG.maxZoom,
            },
          ],
        },
        center: MAP_CONFIG.center,
        zoom: MAP_CONFIG.zoom,
        minZoom: MAP_CONFIG.minZoom,
      });

      map.current.addControl(
        new maplibregl.NavigationControl({
          showCompass: false,
          showZoom: true,
          visualizePitch: false,
        }),
        "bottom-right",
      );

      map.current.on("zoom", () => {
        if (map.current) {
          const zoom = map.current.getZoom();
          setShowGoBackButton(zoom > MAP_CONFIG.minZoom + 0.5);
        }
      });

      map.current.on("load", () => {
        const mapCanvas =
          mapContainer.current?.querySelector(".maplibregl-canvas");
        if (mapCanvas) {
          (mapCanvas as HTMLElement).style.filter = "grayscale(100%)";
        }

        addStyleToDocument("optimized-marker-styles", getMarkerStyles());
        addStyleToDocument("optimized-popup-styles", getPopupStyles());
      });

      return () => {
        clearMarkers(markers, currentPopup);
        map.current?.remove();
        map.current = null;
      };
    } catch (error) {
      console.error("Error initializing map:", error);
    }
  }, []);

  useEffect(() => {
    if (!map.current || isLoading) return;

    clearMarkers(markers, currentPopup);

    if (isInFocusMode && focusedStation) {
      let stationData = null;
      let dataType = "";

      if (focusedStation.type === "rainfall" && rainfallMapData) {
        stationData = rainfallMapData.find(
          (station) => station.id === focusedStation.id,
        );
        dataType = "rainfall";
      } else if (focusedStation.type === "waterLevel" && waterLevelMapData) {
        stationData = waterLevelMapData.find(
          (station) => station.id === focusedStation.id,
        );
        dataType = "waterLevel";
      }

      if (stationData) {
        createMarker({
          map: map.current,
          station: stationData,
          dataType: dataType as "rainfall" | "waterLevel",
          markers,
          currentPopup,
          onMarkerClick: (id, type) => setSelectedStation({ id, type }),
        });
      }
    } else {
      if (
        showWaterLevel &&
        Array.isArray(waterLevelMapData) &&
        waterLevelMapData.length > 0
      ) {
        waterLevelMapData.forEach((station) => {
          createMarker({
            map: map.current,
            station,
            dataType: "waterLevel",
            markers,
            currentPopup,
            onMarkerClick: (id, type) => setSelectedStation({ id, type }),
          });
        });
      }

      if (
        showRainfall &&
        Array.isArray(rainfallMapData) &&
        rainfallMapData.length > 0
      ) {
        rainfallMapData.forEach((station) => {
          createMarker({
            map: map.current,
            station,
            dataType: "rainfall",
            markers,
            currentPopup,
            onMarkerClick: (id, type) => setSelectedStation({ id, type }),
          });
        });
      }
    }
  }, [
    showWaterLevel,
    showRainfall,
    waterLevelMapData,
    rainfallMapData,
    isLoading,
    isInFocusMode,
    focusedStation,
  ]);

  useEffect(() => {
    if (!map.current) return;

    if (focusedStation) {
      let stationData = null;
      if (focusedStation.type === "rainfall" && rainfallMapData) {
        stationData = rainfallMapData.find(
          (station) => station.id === focusedStation.id,
        );
      } else if (focusedStation.type === "waterLevel" && waterLevelMapData) {
        stationData = waterLevelMapData.find(
          (station) => station.id === focusedStation.id,
        );
      }

      if (stationData && stationData.longitude && stationData.latitude) {
        setIsInFocusMode(true);

        map.current.flyTo({
          center: [stationData.longitude, stationData.latitude],
          zoom: 12,
          duration: 1000,
        });

        setSelectedStation({
          id: focusedStation.id,
          type: focusedStation.type,
        });
      }
    } else {
      setIsInFocusMode(false);
    }
  }, [focusedStation, rainfallMapData, waterLevelMapData]);

  if (hasError) {
    return <ErrorDisplay hasError={hasError} />;
  }

  return (
    <div className="relative h-full w-full">
      <div ref={mapContainer} className="h-full w-full" />
      <GoBackButton
        show={showGoBackButton}
        map={map.current}
        onGoBack={() => {
          setIsInFocusMode(false);
          if (onClearFocus) {
            onClearFocus();
          }
        }}
      />
      <MapLegend dataType={dataType} />
      <LoadingOverlay isLoading={isLoading} />
    </div>
  );
};

export default OptimizedMapView;
