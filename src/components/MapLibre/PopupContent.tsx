import { DetailedStationData } from "@/api/mapData";
import { getStatusTextColor } from "./utils";


export const createLoadingPopupContent = (stationName: string): string => {
  return `
    <div class="loading-content" style="position: relative;">
      <!-- Custom Close Button -->
      <button
        onclick="this.closest('.maplibregl-popup').remove()"
        style="position: absolute; top: 8px; right: 8px; width: 24px; height: 24px; background: white; border: 1px solid #ccc; color: #666; font-size: 16px; cursor: pointer; z-index: 1000;"
      >
        ×
      </button>

      <div class="loading-spinner"></div>
      <p>Loading ${stationName}...</p>
    </div>
  `;
};


export const generateRainfallPopupContent = (station: DetailedStationData): string => {
  const rainfallData = [
    { period: '1 hr', value: station.rainfall1h },
    { period: '3 hr', value: station.rainfall3h },
    { period: '6 hr', value: station.rainfall6h },
    { period: '12 hr', value: station.rainfall12h },
    { period: '24 hr', value: station.rainfall24h },
  ];

  return `
    <div style="font-family: 'Lato', sans-serif; background: #ffffff; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); width: 100%; max-width: 320px; position: relative;">
      <!-- Custom Close Button -->
      <button
        onclick="this.closest('.maplibregl-popup').remove()"
        style="position: absolute; top: 8px; right: 8px; width: 30px; height: 30px;  color: #666; font-size: 16px; cursor: pointer; z-index: 1000;"
      >
        ×
      </button>

      <div style="display: flex; flex-direction: row; align-items: center; justify-content: space-between; padding: 16px; border-bottom: 1px solid #e9e9e9; padding-right: 44px;">
        <h2 style="margin: 0; font-size: 18px; font-weight: 600; color: #262626;">Accumulated Rainfall</h2>
      </div>
      <div style="padding: 20px;">
        <div style="margin-bottom: 18px;">
          <h3 style="margin: 0 0 4px; font-size: 15px; font-weight: 500; color: #262626;">${station.stationName || 'Unknown Station'}</h3>
          <p style="margin: 0 0 4px; color: #7b7b7b; font-size: 14px;">${station.basin || 'N/A'}</p>
          <p style="margin-bottom: 16px; font-size: 14px; font-weight: 600;color: ${getStatusTextColor(station.status)}; border-bottom: 1px solid #e9e9e9;">${(station.status && station.status.charAt(0).toUpperCase() + station.status.slice(1).toLowerCase()) || 'Unknown'}</p>
        </div>
        <div style="display: flex; flex-direction: column; gap: 5px;">
          ${rainfallData
            .map(({ period, value }, index, arr) => `
              <div style="display: flex; justify-content: space-between; align-items: center; ${
                index !== arr.length - 1 ? 'border-bottom: 1px solid #e9e9e9;' : ''
              }">
                <span style="color: #262626; font-weight: 500; font-size: 14px;">${period}</span>
                <span style="color: #262626; font-weight: 600; font-size: 14px;">${value ? `${value} mm` : 'n/a'}</span>
              </div>
            `)
            .join('')}
        </div>
      </div>
    </div>
  `;
};


export const generateWaterLevelPopupContent = (station: DetailedStationData): string => {
  return `
    <div style="font-family: 'Inter', sans-serif; padding: 16px; min-width: 280px; background: white; border-radius: 8px; position: relative;">
      <!-- Custom Close Button -->
      <button
        onclick="this.closest('.maplibregl-popup').remove()"
        style="position: absolute; top: 8px; right: 8px; width: 30px; height: 30px;  color: #666; font-size: 16px; cursor: pointer; z-index: 1000;"
      >
        ×
      </button>

      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; padding-right: 36px;">
        <h3 style="margin: 0; font-size: 18px; font-weight: 600; color: #1F2937;">River Watch</h3>
      </div>
      <h4 style="margin: 0 0 8px; font-size: 14px; font-weight: 600; color: #1F2937;">${station.stationName || 'Unknown Station'}</h4>
      <div style="margin-bottom: 4px; font-size: 14px; color: #6B7280; font-style: italic;">${station.basin || 'N/A'}</div>
      <div style="margin-bottom: 16px; font-size: 14px; font-weight: 600; color: ${getStatusTextColor(station.status)}; border-bottom: 1px solid #e9e9e9;">
        ${(station.status && station.status.charAt(0).toUpperCase() + station.status.slice(1).toLowerCase()) || 'Unknown'}
      </div>
      <div style="margin-bottom: 12px; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #e9e9e9;">
        <span style="font-size: 14px; color: #374151; font-weight: 500;">Water Level</span>
        <span style="font-size: 15px; color: #1F2937; font-weight: 600;">${station.waterLevel ? `${station.waterLevel} m` : 'n/a'}</span>
      </div>
      <div style="margin-bottom: 12px; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #e9e9e9;">
        <span style=" font-size: 14px; color: #374151; font-weight: 500;">Warning Level</span>
        <span style="font-size: 15px; color: #1F2937; font-weight: 600;">${station.warningLevel ? `${station.warningLevel} m` : 'n/a'}</span>
      </div>
      <div style="margin-bottom: 0; display: flex; justify-content: space-between; align-items: center;">
        <span style="font-size: 14px; color: #374151; font-weight: 500;">Danger Level</span>
        <span style="font-size: 15px; color: #1F2937; font-weight: 600;">${station.dangerLevel ? `${station.dangerLevel} m` : 'n/a'}</span>
      </div>
    </div>
  `;
};