import { Marker, Popup } from "maplibre-gl";

export interface OptimizedMapViewProps {
  dataType: "waterLevel" | "rainfall" | "both";
  waterLevelData?: Record<string, unknown>[];
  rainfallData?: Record<string, unknown>[];
  isLoading?: boolean;
  selectedStation?: Record<string, unknown>;
  focusedStation?: {
    id: number;
    type: "rainfall" | "waterLevel";
  } | null;
  onClearFocus?: () => void;
}

export interface MarkerState {
  marker: Marker;
  popup: Popup;
  stationId: number;
  stationType: "rainfall" | "waterLevel";
}

export interface SelectedStationState {
  id: number;
  type: "rainfall" | "waterLevel";
}
