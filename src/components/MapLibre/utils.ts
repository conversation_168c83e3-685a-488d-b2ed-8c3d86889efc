
export const getStatusTextColor = (status: string): string => {
  switch (status?.toUpperCase()) {
    case "DANGER LEVEL":
    case "DANGER":
      return "#DC2626";
    case "WARNING LEVEL":
    case "WARNING":
      return "#F59E0B";
    case "BELOW WARNING LEVEL":
    default:
      return "#10B981";
  }
};


export const getMarkerIcon = (status: string, dataType: string): string => {
  const iconMaps = {
    waterLevel: {
      "DANGER LEVEL": "/reds.svg",
      "DANGER": "/reds.svg",
      "WARNING LEVEL": "/yellows.svg",
      "WARNING": "/yellows.svg",
      "BELOW WARNING LEVEL": "/greens.svg",
      default: "/greens.svg"
    },
    rainfall: {
      "DANGER LEVEL": "/Danger.svg",
      "DANGER": "/Danger.svg",
      "WARNING LEVEL": "/yellow.svg",
      "WARNING": "/yellow.svg",
      "BELOW WARNING LEVEL": "/green.svg",
      default: "/green.svg"
    }
  };

  const iconMap = iconMaps[dataType as keyof typeof iconMaps];
  return iconMap?.[status as keyof typeof iconMap] || iconMap?.default || "/green.svg";
};