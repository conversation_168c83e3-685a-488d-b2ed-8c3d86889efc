import { useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import LoginDialog from "../LoginDialog";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const [isLoginOpen, setIsLoginOpen] = useState(false);
  const location = useLocation();
  const { isAuthenticated, loading } = useAuth();


  if (loading) {
    return <div>Loading...</div>;
  }


  if (isAuthenticated) {
    return <>{children}</>;
  }


  if (!isLoginOpen) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }


  return (
    <>
      {children}
      <LoginDialog open={isLoginOpen} onOpenChange={setIsLoginOpen} />
    </>
  );
}
