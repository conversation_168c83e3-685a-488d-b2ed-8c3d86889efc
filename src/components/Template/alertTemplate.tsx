import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import * as Tabs from "@radix-ui/react-tabs";
import { useState, useRef } from "react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FileUpload } from "@/pages/Risk/SendAlert/components/FileUpload";
import { CustomMessage } from "@/pages/Risk/SendAlert/components/CustomMessage";
import {
  useCreateAlertTemplate,
  useUpdateAlertTemplate,
} from "@/api/alertTemplates";
import { useUploadMedia } from "@/api/media";
import { MediaType } from "@/types/media";
import {
  HazardType,
  Severity,
  TemplateType,
  AlertTemplateRequest,
  AlertTemplate,
} from "@/types/alertTemplate";
import { toast } from "sonner";

interface TemplateFormProps {
  trigger: React.ReactNode;
  editData?: AlertTemplate;
  onSuccess?: () => void;
}

interface FormState {
  template_name: string;
  hazard_type: HazardType;
  severity: Severity;
  alert_message: string;
  mail_subject?: string;
}

const initialFormState: FormState = {
  template_name: "",
  hazard_type: HazardType.FIRE,
  severity: Severity.LOW,
  alert_message: "",
  mail_subject: "",
};

interface ApiError {
  response?: {
    data?: {
      detail?: string;
    };
  };
  message?: string;
}

export default function TemplateForm({
  trigger,
  editData,
  onSuccess,
}: TemplateFormProps) {
  const isEditMode = !!editData;

  const getInitialFormState = (): FormState => {
    if (editData) {
      return {
        template_name: editData.template_name,
        hazard_type: editData.hazard_type,
        severity: editData.severity,
        alert_message: editData.alert_message,
        mail_subject: editData.mail_subject || "",
      };
    }
    return initialFormState;
  };

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState<TemplateType>(
    editData?.template_type || TemplateType.SMS,
  );
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [dragOver, setDragOver] = useState(false);
  const [formState, setFormState] = useState<FormState>(getInitialFormState());
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{
    template_name?: string;
    hazard_type?: string;
    severity?: string;
    alert_message?: string;
    mail_subject?: string;
    file?: string;
  }>({});

  const fileInputRef = useRef<HTMLInputElement>(null!);

  const createTemplate = useCreateAlertTemplate();
  const updateTemplate = useUpdateAlertTemplate(editData?.id || 0);
  const uploadMedia = useUploadMedia();

  const handleInputChange = (field: keyof FormState, value: string) => {
    setFormState((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleFileClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files);
      setSelectedFiles((prev) => [...prev, ...newFiles]);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragOver(false);
    const files = event.dataTransfer.files;
    if (files) {
      const newFiles = Array.from(files);
      setSelectedFiles((prev) => [...prev, ...newFiles]);
    }
  };

  const handleFileDelete = (fileToDelete: File) => {
    setSelectedFiles((prev) => prev.filter((file) => file !== fileToDelete));
  };

  const handleDialogOpen = (open: boolean) => {
    setIsDialogOpen(open);
    if (open) {
      setFormState(getInitialFormState());
      setSelectedTab(editData?.template_type || TemplateType.SMS);
      setSelectedFiles([]);
      setErrors({});
    }
  };

  const validateForm = () => {
    const newErrors: {
      template_name?: string;
      hazard_type?: string;
      severity?: string;
      alert_message?: string;
      mail_subject?: string;
      file?: string;
    } = {};
    if (!formState.template_name.trim()) {
      newErrors.template_name = "Template name is required";
    }
    if (!formState.hazard_type) {
      newErrors.hazard_type = "Hazard type is required";
    }
    if (!formState.severity) {
      newErrors.severity = "Severity is required";
    }
    if (!formState.alert_message.trim()) {
      newErrors.alert_message = "Alert message is required";
    }
    if (selectedTab === TemplateType.EMAIL) {
      if (!formState.mail_subject?.trim()) {
        newErrors.mail_subject = "Mail subject is required for email templates";
      }
      if (selectedFiles.length === 0) {
        newErrors.file =
          "At least one file attachment is required for email templates";
      }
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const templatePayload: Partial<AlertTemplateRequest> = {
        template_type: selectedTab,
        template_name: formState.template_name,
        hazard_type: formState.hazard_type,
        severity: formState.severity,
        alert_message: formState.alert_message,
      };

      if (selectedTab === TemplateType.EMAIL) {
        templatePayload.mail_subject = formState.mail_subject;
      }

      if (selectedTab === TemplateType.EMAIL && selectedFiles.length > 0) {
        const mediaResponse = await uploadMedia.mutateAsync({
          file: selectedFiles[0],
          media_type: MediaType.DOCUMENT,
          title: selectedFiles[0].name,
          is_public: true,
        });

        templatePayload.media_id = mediaResponse.id;
      }

      if (isEditMode) {
        await updateTemplate.mutateAsync(
          templatePayload as AlertTemplateRequest,
        );
        toast.success("Template updated successfully");
      } else {
        await createTemplate.mutateAsync(
          templatePayload as AlertTemplateRequest,
        );
        toast.success("Template created successfully");
      }

      setIsDialogOpen(false);
      setFormState(getInitialFormState());
      setSelectedFiles([]);

      if (onSuccess) {
        onSuccess();
      }
    } catch (error: unknown) {
      const apiError = error as ApiError;
      const operation = isEditMode ? "updating" : "creating";
      toast.error(
        apiError.response?.data?.detail ||
          `Failed to ${operation.slice(0, -3)} template`,
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpen}>
      <div onClick={() => handleDialogOpen(true)}>{trigger}</div>
      <DialogContent className="flex h-[800px] max-h-[90vh] min-h-[500px] flex-col overflow-hidden p-0 sm:max-w-[600px]">
        <DialogHeader className="flex-shrink-0 px-8 pt-8">
          <DialogTitle className="poppins-medium text-xl">
            {isEditMode ? "Edit Template" : "Add Template"}
          </DialogTitle>
          <DialogDescription className="sr-only">
            {isEditMode ? "Edit an existing alert template" : "Create a new alert template"}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-x-hidden overflow-y-auto px-8">
          <Tabs.Root
            value={selectedTab}
            onValueChange={(value: string) => {
              if (value === TemplateType.SMS || value === TemplateType.EMAIL) {
                setSelectedTab(value as TemplateType);
              }
            }}
            className="flex flex-col"
          >
            <ToggleGroup
              type="single"
              value={selectedTab}
              onValueChange={(value: string) => {
                if (
                  value === TemplateType.SMS ||
                  value === TemplateType.EMAIL
                ) {
                  setSelectedTab(value as TemplateType);
                }
              }}
              className="sticky top-0 z-10 inline-flex h-9 w-[109px] items-center rounded bg-[#F5F5F5] p-0.5"
            >
              <ToggleGroupItem
                value={TemplateType.SMS}
                className="h-6 flex-1 rounded-sm border-1 border-solid border-[#d0d5dd] px-2 py-0 text-xs data-[state=off]:border-none data-[state=off]:text-[#757575] data-[state=on]:border-[#02475C] data-[state=on]:bg-[#E9F3F0] data-[state=on]:text-[#02475C]"
              >
                <span className="text-xs font-[600] whitespace-nowrap">
                  SMS
                </span>
              </ToggleGroupItem>

              <ToggleGroupItem
                value={TemplateType.EMAIL}
                className="h-6 flex-1 rounded-sm border-1 border-solid border-[#d0d5dd] px-2 py-0 text-xs data-[state=off]:border-none data-[state=off]:text-[#757575] data-[state=on]:border-[#02475C] data-[state=on]:bg-[#E9F3F0] data-[state=on]:text-[#02475C]"
              >
                <span className="text-xs font-[600] whitespace-nowrap">
                  Email
                </span>
              </ToggleGroupItem>
            </ToggleGroup>

            <div className="mt-6">
              <Tabs.Content
                value={TemplateType.SMS}
                className="data-[state=active]:animate-contentShow"
              >
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Template Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      placeholder="Enter Template Name"
                      value={formState.template_name}
                      onChange={(e) => {
                        handleInputChange("template_name", e.target.value);
                        if (e.target.value.trim())
                          setErrors((prev) => ({
                            ...prev,
                            template_name: undefined,
                          }));
                      }}
                      className={`w-full rounded-lg border px-3 py-2 focus:ring-2 focus:ring-[#02475C]/20 focus:outline-none ${errors.template_name ? "border-red-500" : ""}`}
                    />
                    {errors.template_name && (
                      <p className="mt-1 text-xs text-red-500">
                        {errors.template_name}
                      </p>
                    )}
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Hazard Type <span className="text-red-500">*</span>
                      </label>
                      <Select
                        value={formState.hazard_type}
                        onValueChange={(value) => {
                          handleInputChange("hazard_type", value);
                          if (value)
                            setErrors((prev) => ({
                              ...prev,
                              hazard_type: undefined,
                            }));
                        }}
                      >
                        <SelectTrigger className="font-poppins relative h-10 w-full rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                          <SelectValue placeholder="Select hazard type" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(HazardType).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type.charAt(0).toUpperCase() + type.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.hazard_type && (
                        <p className="mt-1 text-xs text-red-500">
                          {errors.hazard_type}
                        </p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Severity <span className="text-red-500">*</span>
                      </label>
                      <Select
                        value={formState.severity}
                        onValueChange={(value) => {
                          handleInputChange("severity", value);
                          if (value)
                            setErrors((prev) => ({
                              ...prev,
                              severity: undefined,
                            }));
                        }}
                      >
                        <SelectTrigger className="font-poppins relative h-10 w-full rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                          <SelectValue placeholder="Select severity" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(Severity).map((level) => (
                            <SelectItem key={level} value={level}>
                              {level.charAt(0).toUpperCase() + level.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.severity && (
                        <p className="mt-1 text-xs text-red-500">
                          {errors.severity}
                        </p>
                      )}
                    </div>
                  </div>
                  <CustomMessage
                    value={formState.alert_message}
                    onChange={(value) => {
                      handleInputChange("alert_message", value);
                      if (value.trim())
                        setErrors((prev) => ({
                          ...prev,
                          alert_message: undefined,
                        }));
                    }}
                  />
                  {errors.alert_message && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors.alert_message}
                    </p>
                  )}
                </div>
              </Tabs.Content>

              <Tabs.Content
                value={TemplateType.EMAIL}
                className="data-[state=active]:animate-contentShow"
              >
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Template Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      placeholder="Enter Template Name"
                      value={formState.template_name}
                      onChange={(e) => {
                        handleInputChange("template_name", e.target.value);
                        if (e.target.value.trim())
                          setErrors((prev) => ({
                            ...prev,
                            template_name: undefined,
                          }));
                      }}
                      className={`w-full rounded-lg border px-3 py-2 focus:ring-2 focus:ring-[#02475C]/20 focus:outline-none ${errors.template_name ? "border-red-500" : ""}`}
                    />
                    {errors.template_name && (
                      <p className="mt-1 text-xs text-red-500">
                        {errors.template_name}
                      </p>
                    )}
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Hazard Type <span className="text-red-500">*</span>
                      </label>
                      <Select
                        value={formState.hazard_type}
                        onValueChange={(value) => {
                          handleInputChange("hazard_type", value);
                          if (value)
                            setErrors((prev) => ({
                              ...prev,
                              hazard_type: undefined,
                            }));
                        }}
                      >
                        <SelectTrigger className="font-poppins relative h-10 w-full rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                          <SelectValue placeholder="Select hazard type" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(HazardType).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type.charAt(0).toUpperCase() + type.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.hazard_type && (
                        <p className="mt-1 text-xs text-red-500">
                          {errors.hazard_type}
                        </p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Severity <span className="text-red-500">*</span>
                      </label>
                      <Select
                        value={formState.severity}
                        onValueChange={(value) => {
                          handleInputChange("severity", value);
                          if (value)
                            setErrors((prev) => ({
                              ...prev,
                              severity: undefined,
                            }));
                        }}
                      >
                        <SelectTrigger className="font-poppins relative h-10 w-full rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                          <SelectValue placeholder="Select severity" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.values(Severity).map((level) => (
                            <SelectItem key={level} value={level}>
                              {level.charAt(0).toUpperCase() + level.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.severity && (
                        <p className="mt-1 text-xs text-red-500">
                          {errors.severity}
                        </p>
                      )}
                    </div>
                  </div>
                  <CustomMessage
                    value={formState.alert_message}
                    onChange={(value) => {
                      handleInputChange("alert_message", value);
                      if (value.trim())
                        setErrors((prev) => ({
                          ...prev,
                          alert_message: undefined,
                        }));
                    }}
                  />
                  {errors.alert_message && (
                    <p className="mt-1 text-xs text-red-500">
                      {errors.alert_message}
                    </p>
                  )}
                  {selectedTab === TemplateType.EMAIL && (
                    <>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Mail Subject <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          placeholder="Enter Mail Subject"
                          value={formState.mail_subject}
                          onChange={(e) => {
                            handleInputChange("mail_subject", e.target.value);
                            if (e.target.value.trim())
                              setErrors((prev) => ({
                                ...prev,
                                mail_subject: undefined,
                              }));
                          }}
                          className={`w-full rounded-lg border px-3 py-2 focus:ring-2 focus:ring-[#02475C]/20 focus:outline-none ${errors.mail_subject ? "border-red-500" : ""}`}
                        />
                        {errors.mail_subject && (
                          <p className="mt-1 text-xs text-red-500">
                            {errors.mail_subject}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Attachments <span className="text-red-500">*</span>
                        </label>
                        <FileUpload
                          showLabel={false}
                          selectedFiles={selectedFiles}
                          dragOver={dragOver}
                          onFileClick={handleFileClick}
                          onFileChange={handleFileChange}
                          onDrop={handleDrop}
                          onDragOver={(e) => {
                            e.preventDefault();
                            setDragOver(true);
                          }}
                          onDragLeave={(e) => {
                            e.preventDefault();
                            setDragOver(false);
                          }}
                          onFileDelete={handleFileDelete}
                          fileInputRef={fileInputRef}
                        />
                        {errors.file && (
                          <p className="mt-1 text-xs text-red-500">
                            {errors.file}
                          </p>
                        )}
                      </div>
                    </>
                  )}
                </div>
              </Tabs.Content>
            </div>
          </Tabs.Root>
        </div>

        <div className="flex-shrink-0 border-gray-200 bg-white px-8 py-6">
          <div className="poppins-regular flex items-center justify-center gap-4">
            <Button
              variant="outline"
              className="w-[120px] rounded-md border border-[#D0D5DD] px-6 py-2 text-[#344054]"
              onClick={() => handleDialogOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              className="w-[120px] rounded-md bg-gradient-to-r from-[#00796B] to-[#004D40] px-6 py-2 text-white"
              onClick={handleSubmit}
              disabled={isLoading}
            >
              {isLoading
                ? isEditMode
                  ? "Updating..."
                  : "Creating..."
                : isEditMode
                  ? "Update"
                  : "Create"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
