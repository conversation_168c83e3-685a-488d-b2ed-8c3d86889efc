import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FileUpload } from "@/pages/Risk/SendAlert/components/FileUpload";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useCreateGroup,
  useUpdateGroup,
  useDownloadTemplate,
} from "@/api/groups";
import { toast } from "sonner";
import { CategoryEnum, SectorEnum, GroupRequest, Group } from "@/types/group";
import { Loader2 } from "lucide-react";

interface GroupDialogProps {
  trigger?: React.ReactNode;
  open: boolean;
  setOpen: (open: boolean) => void;
  editData?: Group | null;
  onSuccess?: () => void;
}

const sectors = [
  { label: "Local Government", value: SectorEnum.GOVERNMENT },
  { label: "Education", value: SectorEnum.EDUCATION },
  { label: "Health", value: SectorEnum.HEALTH },
  { label: "Agriculture", value: SectorEnum.AGRICULTURE },
  { label: "Water Supply", value: SectorEnum.WATER_SUPPLY },
];

const GroupDialog: React.FC<GroupDialogProps> = ({
  trigger,
  open,
  setOpen,
  editData,
  onSuccess,
}) => {
  const [dragOver, setDragOver] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [groupName, setGroupName] = useState(editData?.group_name || "");
  const [sector, setSector] = useState<SectorEnum | "">(editData?.sector || "");
  const [location, setLocation] = useState(editData?.location || "");
  const [errors, setErrors] = useState<{
    groupName?: string;
    location?: string;
    sector?: string;
    file?: string;
  }>({});

  const fileInputRef = useRef<HTMLInputElement>(null!);

  const isEditMode = !!editData;
  const createGroup = useCreateGroup();
  const updateGroup = useUpdateGroup(editData?.id || 0);
  const downloadTemplate = useDownloadTemplate();

  useEffect(() => {
    if (editData) {
      setGroupName(editData.group_name || "");
      setSector(editData.sector || "");
      setLocation(editData.location || "");
      setSelectedFiles([]);
    } else {
      setGroupName("");
      setSector("");
      setLocation("");
      setSelectedFiles([]);
    }
  }, [editData]);

  const handleFileClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files);
      setSelectedFiles((prev) => [...prev, ...newFiles]);

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }

      setErrors((prev) => ({ ...prev, file: undefined }));
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragOver(false);
    const files = event.dataTransfer.files;
    if (files) {
      const newFiles = Array.from(files);
      setSelectedFiles((prev) => [...prev, ...newFiles]);

      setErrors((prev) => ({ ...prev, file: undefined }));
    }
  };

  const handleFileDelete = (fileToDelete: File) => {
    setSelectedFiles((prev) => prev.filter((file) => file !== fileToDelete));
  };

  const handleDownloadTemplate = async () => {
    try {
      const blob = await downloadTemplate.mutateAsync();

      const url = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "contacts_template.xlsx");

      document.body.appendChild(link);

      link.click();

      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success("Template downloaded successfully");
    } catch {
      toast.error("Failed to download template");
    }
  };

  const validateForm = () => {
    const newErrors: {
      groupName?: string;
      location?: string;
      sector?: string;
      file?: string;
    } = {};

    if (!groupName.trim()) {
      newErrors.groupName = "Group name is required";
    }

    if (!location.trim()) {
      newErrors.location = "Location is required";
    }

    if (!sector) {
      newErrors.sector = "Sector is required";
    }

    if (!isEditMode && selectedFiles.length === 0) {
      newErrors.file = "Please upload a contacts file";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    const groupData: GroupRequest = {
      category: CategoryEnum.GROUP,
      group_name: groupName,
      location,
      sector: sector as SectorEnum,
      ...(selectedFiles.length > 0 && { contacts_file: selectedFiles[0] }),
    };

    try {
      if (isEditMode) {
        await updateGroup.mutateAsync(groupData);
        toast.success("Group updated successfully");
      } else {
        await createGroup.mutateAsync(groupData);
        toast.success("Group created successfully");
      }

      setGroupName("");
      setLocation("");
      setSector("");
      setSelectedFiles([]);

      setOpen(false);

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error(
        `Error ${isEditMode ? "updating" : "creating"} group:`,
        error,
      );
      toast.error(`Failed to ${isEditMode ? "update" : "create"} group`);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}

      <DialogContent className="flex max-h-[90vh] min-h-[300px] max-w-md flex-col overflow-hidden p-6">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-lg font-semibold text-[#101828]">
            {isEditMode ? "Edit Group" : "Add Group"}
          </DialogTitle>
          <DialogDescription className="text-sm text-[#6B7280]">
            {isEditMode
              ? "Update the group information and contacts file."
              : ""}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 space-y-5 overflow-x-hidden overflow-y-auto pr-1">
          <div>
            <label className="poppins mb-1 block text-sm text-[#344054]">
              Group Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              placeholder="Enter Group Name"
              className={`focus:border-primary poppins-semibold w-full rounded-md border ${
                errors.groupName ? "border-red-500" : ""
              } px-4 py-2 text-sm text-gray-700 shadow-sm outline-none placeholder:text-[#9D9D9D] focus:ring-1 focus:ring-[#32A57C]`}
              value={groupName}
              onChange={(e) => {
                setGroupName(e.target.value);
                if (e.target.value.trim()) {
                  setErrors((prev) => ({ ...prev, groupName: undefined }));
                }
              }}
            />
            {errors.groupName && (
              <p className="mt-1 text-xs text-red-500">{errors.groupName}</p>
            )}
          </div>

          <div>
            <label className="poppins mb-1 block text-sm text-[#344054]">
              Location <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              placeholder="Enter Location"
              className={`focus:border-primary poppins-semibold w-full rounded-md border ${
                errors.location ? "border-red-500" : ""
              } px-4 py-2 text-sm text-gray-700 shadow-sm outline-none placeholder:text-[#9D9D9D] focus:ring-1 focus:ring-[#32A57C]`}
              value={location}
              onChange={(e) => {
                setLocation(e.target.value);
                if (e.target.value.trim()) {
                  setErrors((prev) => ({ ...prev, location: undefined }));
                }
              }}
            />
            {errors.location && (
              <p className="mt-1 text-xs text-red-500">{errors.location}</p>
            )}
          </div>

          <div>
            <label className="poppins mb-1 block text-sm text-[#344054]">
              Sector <span className="text-red-500">*</span>
            </label>
            <Select
              value={sector}
              onValueChange={(value) => {
                setSector(value as SectorEnum);
                setErrors((prev) => ({ ...prev, sector: undefined }));
              }}
            >
              <SelectTrigger
                className={`font-poppins relative h-10 w-full rounded-lg border ${
                  errors.sector ? "border-red-500" : "border-[#d0d5dd]"
                } bg-white shadow-none [&>svg]:hidden`}
              >
                <SelectValue
                  placeholder="Select Sector"
                  className="font-['Lato'] text-[14px] leading-[18px] font-normal tracking-[0] text-[#555555] placeholder-[#9D9D9D]"
                />
                <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555]">
                  arrow_drop_down
                </span>
              </SelectTrigger>
              <SelectContent>
                {sectors.map((sector) => (
                  <SelectItem key={sector.value} value={sector.value}>
                    {sector.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.sector && (
              <p className="mt-1 text-xs text-red-500">{errors.sector}</p>
            )}
          </div>

          <button
            className="flex cursor-pointer items-center text-blue-600 hover:text-blue-800"
            onClick={handleDownloadTemplate}
            type="button"
          >
            {downloadTemplate.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Downloading template...
              </>
            ) : (
              <img src="/clickhere.svg" alt="download"></img>
            )}
          </button>

          <div>
            <label className="poppins mb-1 block text-sm text-[#344054]">
              Upload User Group <span className="text-red-500">*</span>
            </label>
            <FileUpload
              selectedFiles={selectedFiles}
              dragOver={dragOver}
              onFileClick={handleFileClick}
              onFileChange={handleFileChange}
              onDrop={handleDrop}
              onDragOver={(e) => {
                e.preventDefault();
                setDragOver(true);
              }}
              onDragLeave={(e) => {
                e.preventDefault();
                setDragOver(false);
              }}
              onFileDelete={handleFileDelete}
              fileInputRef={fileInputRef}
              showLabel={false}
            />
            {errors.file && (
              <p className="mt-1 text-xs text-red-500">{errors.file}</p>
            )}
          </div>
        </div>

        <DialogFooter className="poppins-regular mt-6 flex flex-shrink-0 items-center justify-center gap-4">
          <Button
            variant="outline"
            className="rounded-md border border-[#D0D5DD] px-6 py-2 text-[#344054]"
            onClick={() => setOpen(false)}
            disabled={createGroup.isPending || updateGroup.isPending}
          >
            Cancel
          </Button>
          <Button
            className="rounded-md bg-gradient-to-r from-[#00796B] to-[#004D40] px-6 py-2 text-white"
            onClick={handleSubmit}
            disabled={createGroup.isPending || updateGroup.isPending}
          >
            {createGroup.isPending || updateGroup.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditMode ? "Updating..." : "Saving..."}
              </>
            ) : isEditMode ? (
              "Update"
            ) : (
              "Save"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default GroupDialog;
