import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>rigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import PhoneInputLib from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";

interface IndividualDialogProps {
  trigger: React.ReactNode;
  open: boolean;
  setOpen: (open: boolean) => void;
}

const IndividualDialog: React.FC<IndividualDialogProps> = ({
  trigger,
  open,
  setOpen,
}) => {
  const [phone, setPhone] = useState("");

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>

      <DialogContent className="max-w-md p-6 max-h-[90vh] min-h-[300px] flex flex-col overflow-hidden">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-lg font-semibold text-[#101828]">
            Add Individual
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-5 flex-1 overflow-y-auto pr-1 overflow-x-hidden">
          <div>
            <label className="poppins-semibold mb-1 block text-sm text-[#344054]">
              Person Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              placeholder="Enter Person Name"
              className="focus:border-primary poppins-semibold w-full rounded-md border px-4 py-2 text-sm text-gray-700 shadow-sm outline-none placeholder:text-[#9D9D9D] focus:ring-1 focus:ring-[#32A57C]"
            />
          </div>

          <div>
            <label className="poppins-semibold mb-1 block text-sm text-[#344054]">
              Phone <span className="text-red-500">*</span>
            </label>
            <PhoneInputLib
              country={"np"}
              value={phone}
              onChange={setPhone}
              enableSearch
              disableCountryCode={false}
              disableSearchIcon
              inputProps={{
                name: "phone",
                placeholder: "Enter contact detail",
              }}
              inputClass="!w-full !h-10 !text-sm !ps-14 !pe-3 !py-1.5 !border !border-gray-300 !rounded-md focus:!border-[#02475C] focus:!ring-1 focus:!ring-[#02475C] focus:!outline-none"
              containerClass="!w-full"
              buttonClass="!border-none !bg-transparent !ps-2 !me-2"
              dropdownClass="!w-72 !text-sm"
            />
          </div>

          <div>
            <label className="poppins-semibold mb-1 block text-sm text-[#344054]">
              Email <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              placeholder="Enter Email"
              className="focus:border-primary poppins-semibold w-full rounded-md border px-4 py-2 text-sm text-gray-700 shadow-sm outline-none placeholder:text-[#9D9D9D] focus:ring-1 focus:ring-[#32A57C]"
            />
          </div>

          <div>
            <label className="poppins-semibold mb-1 block text-sm text-[#344054]">
              Location <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              placeholder="Enter Location"
              className="focus:border-primary poppins-semibold w-full rounded-md border px-4 py-2 text-sm text-gray-700 shadow-sm outline-none placeholder:text-[#9D9D9D] focus:ring-1 focus:ring-[#32A57C]"
            />
          </div>
        </div>

        <DialogFooter className="poppins-regular mt-6 flex items-center justify-center gap-4 flex-shrink-0">
          <Button
            variant="outline"
            className="rounded-md border border-[#D0D5DD] px-6 py-2 text-[#344054]"
            onClick={() => setOpen(false)}
          >
            Cancel
          </Button>
          <Button className="rounded-md bg-gradient-to-r from-[#00796B] to-[#004D40] px-6 py-2 text-white">
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default IndividualDialog;
