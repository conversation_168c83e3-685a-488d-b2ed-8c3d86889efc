import {
  Monitor,
  LayoutDashboard,
  AlertTriangle,
  Users,
  Send,
  LogOut,
  ChevronDown,
  LucideIcon,
} from "lucide-react";
import { NavLink, useLocation, useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Sidebar, SidebarContent } from "../ui/sidebar";
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useTranslation } from "react-i18next";
import LanguageTranslator from "@/components/LanguageTranslator";

interface SubMenuItem {
  labelKey: string;
  path: string;
}

interface MenuItem {
  labelKey: string;
  path: string;
  icon?: LucideIcon;
  customIcon?: string;
  subItems?: SubMenuItem[];
}

const menuItems: MenuItem[] = [
  { labelKey: "dashboard", icon: LayoutDashboard, path: "/risk/dashboard" },
  {
    labelKey: "realTimeMonitoring",
    icon: Monitor,
    path: "/risk/real-time-monitoring",
  },
  {
    labelKey: "alertTemplate",
    icon: AlertTriangle,
    path: "/risk/alert-template",
    subItems: [
      { labelKey: "all", path: "/risk/alert-template" },
      { labelKey: "sms", path: "/risk/alert-template/sms" },
      { labelKey: "email", path: "/risk/alert-template/email" },
      { labelKey: "ivr", path: "/risk/alert-template/ivr" },
    ],
  },
  {
    labelKey: "groups",
    icon: Users,
    path: "/risk/groups/group",
  },
  {
    labelKey: "sendAlert",
    icon: Send,
    path: "/risk/send-alert",
    subItems: [
      {
        labelKey: "manualAlert",
        path: "/risk/send-alert/manual",
      },
      {
        labelKey: "automaticAlert",
        path: "/risk/send-alert/automatic",
      },
    ],
  },
  {
    labelKey: "alertLogs",
    path: "/risk/alert-logs",
    customIcon: "/logs.svg",
  },
];

export function AppSidebar() {
  const { logout } = useAuth();
  const { t } = useTranslation();
  const [expandedItem, setExpandedItem] = useState<string | null>(null);
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    if (location.pathname === "/risk" || location.pathname === "/risk/") {
      navigate("/risk/dashboard");
    }

    if (location.pathname === "/risk/groups") {
      navigate("/risk/groups/group");
    }

    const currentSection = menuItems.find((item) =>
      location.pathname.includes(item.path),
    );
    if (currentSection?.subItems) {
      setExpandedItem(currentSection.labelKey);
    }
  }, [location.pathname, navigate]);

  const isExactAlertTemplatePath = location.pathname === "/risk/alert-template";
  const isInAlertTemplateSection = location.pathname.includes(
    "/risk/alert-template",
  );
  const isInGroupsSection = location.pathname.includes("/risk/groups");
  const isInSendAlertSection = location.pathname.includes("/risk/send-alert");
  const isDashboard = location.pathname === "/risk/dashboard";

  const handleMenuClick = (item: MenuItem, e: React.MouseEvent) => {
    e.preventDefault();
    if (item.subItems) {
      if (expandedItem === item.labelKey) {
        setExpandedItem(null);
      } else {
        setExpandedItem(item.labelKey);
        navigate(item.subItems[0].path);
      }
    } else {
      navigate(item.path);
    }
  };

  return (
    <Sidebar className="fixed top-0 left-0 z-50 flex h-screen w-64 flex-col justify-between bg-white shadow-[0px_4px_11px_#00000040]">
      <SidebarContent className="flex h-full flex-col">
        <div className="mb-4 flex items-center justify-between py-3 pr-3 pl-3">
          <a href="/">
            <img src="/PrepareLogo.svg" alt="Prepare Logo" className="h-8" />
          </a>
          <LanguageTranslator />
        </div>

        <nav className="flex flex-col">
          {menuItems.map((item) => (
            <div
              key={item.labelKey}
              className={cn(
                "rounded-lg",
                ((item.labelKey === "alertTemplate" &&
                  isInAlertTemplateSection) ||
                  (item.labelKey === "groups" && isInGroupsSection) ||
                  (item.labelKey === "sendAlert" && isInSendAlertSection)) &&
                  "bg-[#F4F7FE]",
              )}
            >
              <NavLink
                to={item.path}
                className={({ isActive }) =>
                  cn(
                    "poppins-regular flex w-full items-center justify-between gap-2 rounded-lg px-4 py-[10px] text-sm text-[#262626] hover:text-[#02475C]",
                    ((isActive && !item.subItems) ||
                      (item.labelKey === "dashboard" && isDashboard)) &&
                      "poppins-semibold bg-[#F4F7FE] text-[#02475C]",
                    (item.labelKey === "alertTemplate" &&
                      isInAlertTemplateSection) ||
                      (item.labelKey === "groups" && isInGroupsSection) ||
                      (item.labelKey === "sendAlert" && isInSendAlertSection)
                      ? "poppins-semibold bg-[#F4F7FE] text-[#02475C]"
                      : "",
                  )
                }
                onClick={(e) => handleMenuClick(item, e)}
              >
                {({ isActive }) => (
                  <>
                    <div className="flex items-center gap-2">
                      {item.customIcon ? (
                        <img
                          src={item.customIcon}
                          alt={t(item.labelKey)}
                          className={cn(
                            "h-5 w-5",
                            ((isActive && !item.subItems) ||
                              (item.labelKey === "dashboard" && isDashboard) ||
                              (item.labelKey === "alertTemplate" &&
                                isInAlertTemplateSection) ||
                              (item.labelKey === "groups" &&
                                isInGroupsSection) ||
                              (item.labelKey === "sendAlert" &&
                                isInSendAlertSection)) &&
                              "brightness-0 saturate-100",
                          )}
                          style={{
                            filter:
                              (isActive && !item.subItems) ||
                              (item.labelKey === "dashboard" && isDashboard) ||
                              (item.labelKey === "alertTemplate" &&
                                isInAlertTemplateSection) ||
                              (item.labelKey === "groups" &&
                                isInGroupsSection) ||
                              (item.labelKey === "sendAlert" &&
                                isInSendAlertSection)
                                ? "brightness(0) saturate(100%) invert(14%) sepia(85%) saturate(1352%) hue-rotate(174deg) brightness(95%) contrast(96%)"
                                : "none",
                          }}
                        />
                      ) : item.icon ? (
                        <item.icon className="h-5 w-5" />
                      ) : null}
                      <span>{t(item.labelKey)}</span>
                    </div>
                    {item.subItems && (
                      <ChevronDown
                        className={cn(
                          "h-4 w-4 transition-transform",
                          expandedItem === item.labelKey && "rotate-180",
                        )}
                      />
                    )}
                  </>
                )}
              </NavLink>
              {item.subItems && expandedItem === item.labelKey && (
                <div
                  className={cn(
                    "ml-9 flex flex-col gap-1 py-1",
                    ((item.labelKey === "alertTemplate" &&
                      isInAlertTemplateSection) ||
                      (item.labelKey === "groups" && isInGroupsSection) ||
                      (item.labelKey === "sendAlert" &&
                        isInSendAlertSection)) &&
                      "rounded-lg bg-[#F4F7FE]",
                  )}
                >
                  {item.subItems.map((subItem) => (
                    <NavLink
                      key={subItem.labelKey}
                      to={subItem.path}
                      className={({ isActive }) =>
                        cn(
                          "poppins-regular rounded-lg px-4 py-2 text-sm text-[#262626] hover:text-[#02475C]",
                          (subItem.labelKey === "all" &&
                            isExactAlertTemplatePath) ||
                            (subItem.labelKey !== "all" && isActive)
                            ? "poppins-semibold bg-[#F4F7FE] text-[#02475C]"
                            : "",
                        )
                      }
                    >
                      {t(subItem.labelKey)}
                    </NavLink>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>

        <div className="mt-auto flex items-center gap-3 border-t p-3">
          <img
            src="/user.svg"
            alt="User Avatar"
            className="h-10 w-10 rounded-full"
          />
          <div className="text-sm">
            <p className="poppins-regular">Username</p>
            <p className="text-xs text-gray-500"><EMAIL></p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="ml-auto"
            onClick={logout}
          >
            <LogOut className="h-4 w-4" />
          </Button>
        </div>
      </SidebarContent>
    </Sidebar>
  );
}
