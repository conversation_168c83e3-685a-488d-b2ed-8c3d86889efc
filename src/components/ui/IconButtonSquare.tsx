import { FunctionComponent } from "react";

interface IconButtonSquareProps {
  icon: string;
  onClick?: () => void;
}

const IconButtonSquare: FunctionComponent<IconButtonSquareProps> = ({
  icon,
  onClick,
}) => {
  return (
    <button
      onClick={onClick}
      className="flex h-10 w-10 items-center justify-center rounded-lg border border-[#d9d9d9] bg-white p-2 text-xl text-[#7b7b7b] transition hover:bg-gray-50"
      style={{ fontFamily: "Material Symbols Rounded" }}
    >
      {icon}
    </button>
  );
};

export default IconButtonSquare;
