import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";

const Pagination = ({ className, ...props }: React.ComponentProps<"nav">) => (
  <nav
    role="navigation"
    aria-label="pagination"
    className={cn("mx-auto flex w-full justify-center", className)}
    {...props}
  />
);
Pagination.displayName = "Pagination";

const PaginationContent = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    className={cn("flex flex-row items-center gap-1", className)}
    {...props}
  />
));
PaginationContent.displayName = "PaginationContent";

const PaginationItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn("", className)} {...props} />
));
PaginationItem.displayName = "PaginationItem";

type PaginationLinkProps = {
  isActive?: boolean;
  size?: "default" | "sm" | "lg" | "icon";
} & React.ComponentProps<"button">;

const PaginationLink = ({
  className,
  isActive,
  size = "icon",
  children,
  ...props
}: PaginationLinkProps) => (
  <button
    aria-current={isActive ? "page" : undefined}
    className={cn(
      buttonVariants({
        variant: isActive ? "outline" : "ghost",
        size,
      }),
      "font-inter h-8 w-8 rounded-full p-0 text-sm font-normal",
      isActive
        ? "border-[#02475c] bg-[#e9f3f0] text-[#02475c]"
        : "text-[#667085] hover:bg-gray-50",
      className,
    )}
    {...props}
  >
    {children}
  </button>
);
PaginationLink.displayName = "PaginationLink";

const PaginationPrevious = ({
  className,
  ...props
}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to previous page"
    size="default"
    className={cn(
      "font-inter h-8 w-auto gap-1 rounded-lg pr-2.5 pl-2.5 text-sm font-normal text-[#667085] hover:bg-gray-50",
      className,
    )}
    {...props}
  >
    <ChevronLeft className="h-4 w-4" />
    <span>Prev</span>
  </PaginationLink>
);
PaginationPrevious.displayName = "PaginationPrevious";

const PaginationNext = ({
  className,
  ...props
}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to next page"
    size="default"
    className={cn(
      "font-inter h-8 w-auto gap-1 rounded-lg pr-2.5 pl-2.5 text-sm font-normal text-[#667085] hover:bg-gray-50",
      className,
    )}
    {...props}
  >
    <span>Next</span>
    <ChevronRight className="h-4 w-4" />
  </PaginationLink>
);
PaginationNext.displayName = "PaginationNext";

const PaginationEllipsis = ({
  className,
  ...props
}: React.ComponentProps<"span">) => (
  <span
    aria-hidden
    className={cn(
      "font-inter flex h-8 w-8 items-center justify-center text-sm font-normal text-[#667085]",
      className,
    )}
    {...props}
  >
    <span>…</span>
    <span className="sr-only">More pages</span>
  </span>
);
PaginationEllipsis.displayName = "PaginationEllipsis";

const EnhancedPagination = ({
  currentPage = 3,
  totalPages = 20,
  itemsPerPage = 10,
  onPageChange,
  onItemsPerPageChange,
}: {
  currentPage?: number;
  totalPages?: number;
  itemsPerPage?: number;
  totalItems?: number;
  onPageChange?: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
}) => {
  const handlePageChange = (page: number) => {
    if (onPageChange) {
      onPageChange(page);
    }
  };

  const handleItemsPerPageChange = (value: number) => {
    if (onItemsPerPageChange) {
      onItemsPerPageChange(value);
    }
  };

  return (
    <div className="font-inter mt-3 ml-4 flex h-9 w-full max-w-[1400px] items-center justify-between">

      <div className="flex items-center gap-2">
        <select
          value={itemsPerPage}
          onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
          className="h-10 px-3 text-sm font-normal text-black"
        >
          <option value={10}>10 per page</option>
          <option value={20}>20 per page</option>
          <option value={50}>50 per page</option>
          <option value={100}>100 per page</option>
        </select>
      </div>


      <div className="mr-4 flex items-center gap-2">

        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          className="flex items-center gap-1 rounded-lg px-2 py-2 text-sm font-normal text-[#667085] hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="font-semibold text-[#02475c]">Prev</span>
        </button>


        <div className="flex items-center gap-2">
          {currentPage > 3 && (
            <>
              <button
                onClick={() => handlePageChange(1)}
                className="flex h-8 w-8 items-center justify-center rounded-full text-sm font-normal text-[#667085] hover:bg-gray-50"
              >
                1
              </button>
              {currentPage > 4 && (
                <span className="flex h-8 w-8 items-center justify-center text-sm font-normal text-[#667085]">
                  …
                </span>
              )}
            </>
          )}

          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNum;
            if (currentPage <= 3) {
              pageNum = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = currentPage - 2 + i;
            }

            if (pageNum < 1 || pageNum > totalPages) return null;

            const isActive = pageNum === currentPage;

            return (
              <button
                key={pageNum}
                onClick={() => handlePageChange(pageNum)}
                className={cn(
                  "flex h-8 w-8 items-center justify-center rounded-full text-sm font-normal",
                  isActive
                    ? "  bg-[#e9f3f0] text-[#02475c]"
                    : "text-[#667085] hover:bg-gray-50",
                )}
              >
                {pageNum}
              </button>
            );
          })}

          {currentPage < totalPages - 2 && (
            <>
              {currentPage < totalPages - 3 && (
                <span className="flex h-8 w-8 items-center justify-center text-sm font-normal text-[#667085]">
                  …
                </span>
              )}
              <button
                onClick={() => handlePageChange(totalPages)}
                className="flex h-8 w-8 items-center justify-center rounded-full text-sm font-normal text-[#667085] hover:bg-gray-50"
              >
                {totalPages}
              </button>
            </>
          )}
        </div>


        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
          className="flex items-center gap-1 rounded-lg px-2 py-2 text-sm font-normal text-[#667085] hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
        >
          <span className="font-semibold text-[#02475c]">Next</span>
          <ChevronRight className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};

export {
  Pagination,
  PaginationContent,
  PaginationLink,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
  EnhancedPagination,
};
