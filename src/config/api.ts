export const API_BASE_URL =
  process.env.NODE_ENV === "development"
    ? ""
    : "https://getprepare-api.optisyslab.com";

export const API_ROUTES = {
  auth: {
    login: "/api/auth/login/",
    refresh: "/api/auth/refresh/",
    protected: "/api/auth/protected/",
    user: "/api/auth/user/",
    userById: (id: number) => `/api/auth/user/${id}/`,
  },
  alertTemplate: {
    list: "/api/alert/templates/",
    create: "/api/alert/templates/",
    getById: (id: number) => `/api/alert/templates/${id}/`,
    update: (id: number) => `/api/alert/templates/${id}/`,
    delete: (id: number) => `/api/alert/templates/${id}/`,
  },
  sendAlert: {
    list: "/api/sendAlert/alerts/",
    create: "/api/sendAlert/alerts/",
    getById: (id: number) => `/api/sendAlert/alerts/${id}/`,
    update: (id: number) => `/api/sendAlert/alerts/${id}/`,
    delete: (id: number) => `/api/sendAlert/alerts/${id}/`,
  },
  groups: {
    list: "/api/groups/",
    create: "/api/groups/create/",
    getById: (id: number) => `/api/groups/groups/${id}/`,
    update: (id: number) => `/api/groups/groups/${id}/`,
    delete: (id: number) => `/api/groups/groups/${id}/`,
    downloadTemplate: "/api/groups/download-template/",
  },
  media: {
    upload: "/api/media/upload/",
  },
};
