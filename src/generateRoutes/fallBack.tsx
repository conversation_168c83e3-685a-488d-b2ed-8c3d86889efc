import { Loader2 } from "lucide-react";

export default function Fallback() {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/80 backdrop-blur-sm">
      <div className="flex flex-col items-center gap-2">
        <Loader2 className="h-10 w-10 animate-spin text-[#02475C]" />
        <p className="poppins-medium text-sm text-[#434343]">Loading...</p>
      </div>
    </div>
  );
}
