import { ReactNode, Suspense } from "react";
import { Route, Routes } from "react-router-dom";
import Fallback from "./fallBack";
import { IRoute } from "../types/route";

interface IGenerateRouteParams {
  routes: IRoute[];
  fallback?: ReactNode;
}

export default function GenerateRoutes({
  routes,
  fallback = <Fallback />,
}: IGenerateRouteParams) {
  return (
    <Suspense fallback={fallback}>
      <Routes>
        {routes?.map((route) => (
          <Route
            key={route.path}
            path={route.path}
            element={
              <Suspense
                fallback={route.fallback ? <route.fallback /> : fallback}
              >
                <route.component />
              </Suspense>
            }
          />
        ))}
      </Routes>
    </Suspense>
  );
}
