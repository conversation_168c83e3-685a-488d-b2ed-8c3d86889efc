import { useState, useCallback } from 'react';
import { useSendAlertsPaginated } from '@/api/sendAlert';
import { useStatusCount } from '@/hooks/useStatusCount';
import { PaginationParams } from '@/lib/utils';

export function useDashboardAlertData() {
  const [alertParams, setAlertParams] = useState<PaginationParams>({ page: 1, page_size: 50 });
  const { data: alertResponse, isLoading, error, refetch } = useSendAlertsPaginated(alertParams);

  const alerts = alertResponse?.results || [];
  const alertCount = alertResponse?.count || 0;

  const loadMoreAlerts = useCallback(() => {
    if (alertResponse?.next) {
      setAlertParams(prev => ({
        ...prev,
        page: (prev.page || 1) + 1
      }));
    }
  }, [alertResponse?.next]);

  const refreshAlerts = useCallback(() => {
    setAlertParams({ page: 1, page_size: 50 });
    return refetch();
  }, [refetch]);

  return {
    alerts,
    alertCount,
    isLoading,
    error,
    hasMore: !!alertResponse?.next,
    loadMoreAlerts,
    refreshAlerts,
  };
}

export function useDashboardData() {
  const alertHook = useDashboardAlertData();
  const statusCountHook = useStatusCount();

  const rainfallCounts = {
    belowWarning: statusCountHook.rainfallStatusCount["BELOW WARNING LEVEL"] || 0,
    warning: ((statusCountHook.rainfallStatusCount as any)["WARNING LEVEL"] || 0) + ((statusCountHook.rainfallStatusCount as any)["WARNING"] || 0),
    danger: ((statusCountHook.rainfallStatusCount as any)["DANGER LEVEL"] || 0) + ((statusCountHook.rainfallStatusCount as any)["DANGER"] || 0),
  };

  const waterLevelCounts = {
    belowWarning: statusCountHook.waterLevelStatusCount["BELOW WARNING LEVEL"] || 0,
    warning: ((statusCountHook.waterLevelStatusCount as any)["WARNING LEVEL"] || 0) + ((statusCountHook.waterLevelStatusCount as any)["WARNING"] || 0),
    danger: ((statusCountHook.waterLevelStatusCount as any)["DANGER LEVEL"] || 0) + ((statusCountHook.waterLevelStatusCount as any)["DANGER"] || 0),
  };

  const overallCounts = {
    belowWarning: rainfallCounts.belowWarning + waterLevelCounts.belowWarning,
    warning: rainfallCounts.warning + waterLevelCounts.warning,
    danger: rainfallCounts.danger + waterLevelCounts.danger,
  };

  const refreshAllData = useCallback(async () => {
    await Promise.all([
      alertHook.refreshAlerts(),
      statusCountHook.refreshRainfallStatus(),
      statusCountHook.refreshWaterLevelStatus(),
    ]);
  }, [alertHook.refreshAlerts, statusCountHook.refreshRainfallStatus, statusCountHook.refreshWaterLevelStatus]);

  const isAnyLoading = alertHook.isLoading || statusCountHook.isLoadingRainfall || statusCountHook.isLoadingWaterLevel;
  const hasAnyError = alertHook.error || statusCountHook.errorRainfall || statusCountHook.errorWaterLevel;

  return {
    alerts: alertHook.alerts,
    alertCount: alertHook.alertCount,
    isLoadingAlerts: alertHook.isLoading,

    rainfallCounts,
    waterLevelCounts,
    overallCounts,

    isAnyLoading,
    hasAnyError,

    refreshAllData,
    refreshAlerts: alertHook.refreshAlerts,
  };
}
