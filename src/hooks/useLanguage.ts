import { useTranslation } from "react-i18next";

export const useLanguage = () => {
  const { i18n, t } = useTranslation();

  const changeLanguage = (language: string) => {
    i18n.changeLanguage(language);
  };

  const getCurrentLanguage = () => {
    return i18n.language;
  };

  const isNepali = () => {
    return i18n.language === "ne";
  };

  const isEnglish = () => {
    return i18n.language === "en";
  };

  return {
    t,
    changeLanguage,
    getCurrentLanguage,
    isNepali,
    isEnglish,
    currentLanguage: i18n.language,
  };
};
