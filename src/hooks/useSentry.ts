import { useCallback } from "react";
import {
  captureError,
  captureSentryMessage,
  setSentryUser,
  setSentryTag,
  setSentryContext,

} from "@/lib/sentry";


export const useSentry = () => {
  const reportError = useCallback((error: Error, context?: Record<string, any>) => {
    captureError(error, context);
  }, []);


  const reportMessage = useCallback((message: string, level?: "fatal" | "error" | "warning" | "log" | "info" | "debug", context?: Record<string, any>) => {
    captureSentryMessage(message, level, context);
  }, []);


  const setUser = useCallback((user: {
    id?: string;
    email?: string;
    username?: string;
    [key: string]: any;
  }) => {
    setSentryUser(user);
  }, []);


  const setTag = useCallback((key: string, value: string) => {
    setSentryTag(key, value);
  }, []);


  const setContext = useCallback((key: string, context: Record<string, any>) => {
    setSentryContext(key, context);
  }, []);

  return {
    reportError,
    reportMessage,
    setUser,
    setTag,
    setContext,
  };
};
