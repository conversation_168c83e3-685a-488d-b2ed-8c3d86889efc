import { useQuery } from "@tanstack/react-query";
import { fetchRainfallStatusCount, fetchWaterLevelStatusCount, StatusCount } from "@/api/mapData";


const statusCountKeys = {
  all: ['statusCount'] as const,
  rainfall: () => [...statusCountKeys.all, 'rainfall'] as const,
  waterLevel: () => [...statusCountKeys.all, 'waterLevel'] as const,
} as const;

interface UseStatusCountReturn {
  rainfallStatusCount: StatusCount;
  waterLevelStatusCount: StatusCount;
  isLoadingRainfall: boolean;
  isLoadingWaterLevel: boolean;
  errorRainfall: string | null;
  errorWaterLevel: string | null;
  lastUpdatedRainfall: string | null;
  lastUpdatedWaterLevel: string | null;
  refreshRainfallStatus: () => Promise<void>;
  refreshWaterLevelStatus: () => Promise<void>;
}


const useRainfallStatusCount = () => {
  return useQuery({
    queryKey: statusCountKeys.rainfall(),
    queryFn: fetchRainfallStatusCount,

    refetchInterval: 15 * 60 * 1000, 
    refetchOnWindowFocus: true,
  });
};

const useWaterLevelStatusCount = () => {
  return useQuery({
    queryKey: statusCountKeys.waterLevel(),
    queryFn: fetchWaterLevelStatusCount,

    refetchInterval: 15 * 60 * 1000, 
    refetchOnWindowFocus: true,
  });
};

export const useStatusCount = (): UseStatusCountReturn => {
  const {
    data: rainfallResponse,
    isLoading: isLoadingRainfall,
    error: rainfallError,
    refetch: refetchRainfall
  } = useRainfallStatusCount();

  const {
    data: waterLevelResponse,
    isLoading: isLoadingWaterLevel,
    error: waterLevelError,
    refetch: refetchWaterLevel
  } = useWaterLevelStatusCount();


  const rainfallStatusCount = rainfallResponse?.status_count || {};
  const waterLevelStatusCount = waterLevelResponse?.status_count || {};
  const lastUpdatedRainfall = rainfallResponse?.last_updated || null;
  const lastUpdatedWaterLevel = waterLevelResponse?.last_updated || null;


  const errorRainfall = rainfallError instanceof Error ? rainfallError.message :
    (rainfallError ? 'Failed to fetch rainfall status count' : null);
  const errorWaterLevel = waterLevelError instanceof Error ? waterLevelError.message :
    (waterLevelError ? 'Failed to fetch water level status count' : null);


  const refreshRainfallStatus = async () => {
    await refetchRainfall();
  };

  const refreshWaterLevelStatus = async () => {
    await refetchWaterLevel();
  };

  return {
    rainfallStatusCount,
    waterLevelStatusCount,
    isLoadingRainfall,
    isLoadingWaterLevel,
    errorRainfall,
    errorWaterLevel,
    lastUpdatedRainfall,
    lastUpdatedWaterLevel,
    refreshRainfallStatus,
    refreshWaterLevelStatus,
  };
};
