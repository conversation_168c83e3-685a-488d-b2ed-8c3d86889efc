import axios from "axios";
import { API_BASE_URL, API_ROUTES } from "@/config/api";
import { TOKEN_STORAGE_KEYS } from "@/api/auth";
import { captureError, setSentryContext } from "@/lib/sentry";

export const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(TOKEN_STORAGE_KEYS.ACCESS_TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    captureError(error, {
      request: {
        type: "API Request Error",
        url: error.config?.url,
        method: error.config?.method,
      },
    });
    return Promise.reject(error);
  },
);

axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    const isLoginRequest = originalRequest?.url?.includes(
      API_ROUTES.auth.login,
    );

    if (!isLoginRequest || error.response?.status !== 401) {
      const errorContext = {
        response: {
          type: "API Response Error",
          url: originalRequest?.url,
          method: originalRequest?.method,
          status: error.response?.status,
          statusText: error.response?.statusText,
          responseData: error.response?.data,
          requestData: originalRequest?.data,
        },
      };

      setSentryContext("API Error", errorContext);

      if (!(error.response?.status === 401 && originalRequest?._retry)) {
        captureError(error, errorContext);
      }
    }

    if (
      error.response?.status === 401 &&
      !isLoginRequest &&
      !originalRequest._retry
    ) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem(
          TOKEN_STORAGE_KEYS.REFRESH_TOKEN,
        );
        if (!refreshToken) {
          throw new Error("No refresh token available");
        }

        const response = await axios.post(`${API_BASE_URL}/api/auth/refresh/`, {
          refresh: refreshToken,
        });

        const { access } = response.data;
        localStorage.setItem(TOKEN_STORAGE_KEYS.ACCESS_TOKEN, access);

        originalRequest.headers.Authorization = `Bearer ${access}`;
        return axios(originalRequest);
      } catch (refreshError) {
        captureError(refreshError as Error, {
          refresh: {
            type: "Token Refresh Error",
            originalUrl: originalRequest?.url,
          },
        });

        localStorage.removeItem(TOKEN_STORAGE_KEYS.ACCESS_TOKEN);
        localStorage.removeItem(TOKEN_STORAGE_KEYS.REFRESH_TOKEN);
        window.location.href = "/";
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  },
);
