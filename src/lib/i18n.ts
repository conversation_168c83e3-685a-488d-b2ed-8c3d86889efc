import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

const resources = {
  en: {
    translation: {
      belowWarning: "Below Warning",
      warning: "Warning",
      danger: "Danger",
      alertSent: "Alert Sent",

      overall: "Overall",
      riverWatch: "River Watch",
      rainfallWatch: "Rainfall Watch",

      searchStations: "Search stations or place",

      loadingMap: "Loading map...",
      failedToLoad: "Failed to load data. Please try again.",
      retry: "Retry",

      dataSource: "Data Source",
      lastUpdated: "Last updated",
      rainfallUpdated: "Rainfall updated",
      riverUpdated: "River updated",

      riverWatchTitle: "River Watch",
      waterLevel: "Water Level",
      status: "Status",
      normal: "Normal",

      rainfallWatchTitle: "Rainfall Watch",
      rainfall: "Rainfall",
      measurement: "Measurement",

      temperature: "Temperature",
      humidity: "Humidity",
      windSpeed: "Wind Speed",
      pressure: "Pressure",

      loading: "Loading...",
      noData: "No data available",
      error: "Error",
      refresh: "Refresh",

      dashboard: "Dashboard",
      realTimeMonitoring: "Real Time Monitoring",
      alertTemplate: "Alert Template",
      groups: "Groups",
      sendAlert: "Send Alert",
      all: "All",
      sms: "SMS",
      email: "Email",
      ivr: "IVR",
      manualAlert: "Manual Alert",
      automaticAlert: "Automatic Alert",
      alertLogs: "Alert Logs",

      legendWaterLevel: "Water Level",
      legendRainfall: "Rainfall",

      searchRivers: "Search rivers...",
      searchRainfall: "Search rainfall...",
      noRiversFound: "No rivers found matching",
      noRainfallFound: "No rainfall data found matching",
      result: "result",
      results: "results",

      "1hr": "1 hr",
      "3hr": "3 hr",
      "6hr": "6 hr",
      "12hr": "12 hr",
      "24hr": "24 hr",

      showAll: "Show All",
      showing: "Showing",
      of: "of",
      locations: "locations",

      weatherLoading: "Loading...",
      partialCloudy: "Partial Cloudy",
      noWeatherData: "N/A",
      noRainfallData: "No rainfall data available",

      realTimeMonitoringTitle: "Real Time Monitoring",
      totalStations: "Total Stations",
      search: "Search",

      serialNumber: "S.N.",
      basinName: "Basin Name",
      stationIndex: "Station Index",
      stationName: "Station Name",
      waterLevelM: "Water Level (m)",
      warningLevelM: "Warning Level(m)",
      dangerLevelM: "Danger Level(m)",
      tableStatus: "Status",

      loadingRainfallData: "Loading rainfall data...",
      loadingWaterLevelData: "Loading water level data...",
      failedToFetchRainfall:
        "Failed to fetch rainfall data. Please try again later.",
      failedToFetchWaterLevel:
        "Failed to fetch water level data. Please try again later.",
      pleaseRetryLater: "Please try again later.",
      noDataFound: "No data found",
      unknown: "Unknown",
      unknownStation: "Unknown Station",

      warningNote: "Note: Warning level for rainfall (mm):",
      mmIn1hr: "60 mm in 1 hr",
      mmIn3hr: "80 mm in 3 hr",
      mmIn6hr: "100 mm in 6 hr",
      mmIn12hr: "120 mm in 12 hr",
      mmIn24hr: "140 mm in 24 hr",

      // Homepage Navbar
      home: "Home",
      about: "About",
      digest: "Digest",
      getInvolved: "Get Involved",
      login: "Login",
      logout: "Logout",

      stayUpToDate: "Stay up to date",
      newsletterDescription:
        "Join our newsletter to stay up to date on features and releases.",
      enterYourEmail: "Enter your email",
      subscribe: "Subscribe",
      technicalPartner: "Technical Partner",
      designAssociatesNepal: "Design Associates Nepal",
    },
  },
  ne: {
    translation: {
      belowWarning: "चेतावनी मुनि",
      warning: "चेतावनी",
      danger: "खतरा",
      alertSent: "अलर्ट पठाइयो",

      overall: "समग्र",
      riverWatch: "नदी निगरानी",
      rainfallWatch: "वर्षा निगरानी",

      searchStations: "स्टेशन वा स्थान खोज्नुहोस्",

      loadingMap: "नक्सा लोड हुँदै...",
      failedToLoad: "डाटा लोड गर्न असफल। कृपया फेरि प्रयास गर्नुहोस्।",
      retry: "फेरि प्रयास गर्नुहोस्",

      dataSource: "डाटा स्रोत",
      lastUpdated: "अन्तिम अपडेट",
      rainfallUpdated: "वर्षा अपडेट",
      riverUpdated: "नदी अपडेट",

      riverWatchTitle: "नदी निगरानी",
      waterLevel: "पानीको सतह",
      status: "स्थिति",
      normal: "सामान्य",

      rainfallWatchTitle: "वर्षा निगरानी",
      rainfall: "वर्षा",
      measurement: "मापन",

      temperature: "तापक्रम",
      humidity: "आर्द्रता",
      windSpeed: "हावाको गति",
      pressure: "दबाब",

      loading: "लोड हुँदै...",
      noData: "कुनै डाटा उपलब्ध छैन",
      error: "त्रुटि",
      refresh: "रिफ्रेश",

      dashboard: "ड्यासबोर्ड",
      realTimeMonitoring: "वास्तविक समय निगरानी",
      alertTemplate: "अलर्ट टेम्प्लेट",
      groups: "समूहहरू",
      sendAlert: "अलर्ट पठाउनुहोस्",
      all: "सबै",
      sms: "एसएमएस",
      email: "इमेल",
      ivr: "आईभीआर",
      manualAlert: "म्यानुअल अलर्ट",
      automaticAlert: "स्वचालित अलर्ट",
      alertLogs: "अलर्ट लगहरू",

      legendWaterLevel: "पानीको सतह",
      legendRainfall: "वर्षा",

      searchRivers: "नदीहरू खोज्नुहोस्...",
      searchRainfall: "वर्षा खोज्नुहोस्...",
      noRiversFound: "मिल्दो नदी फेला परेन",
      noRainfallFound: "मिल्दो वर्षा डाटा फेला परेन",
      result: "परिणाम",
      results: "परिणामहरू",

      "1hr": "१ घण्टा",
      "3hr": "३ घण्टा",
      "6hr": "६ घण्टा",
      "12hr": "१२ घण्टा",
      "24hr": "२४ घण्टा",

      showAll: "सबै देखाउनुहोस्",
      showing: "देखाइँदै",
      of: "को",
      locations: "स्थानहरू",

      weatherLoading: "लोड हुँदै...",
      partialCloudy: "आंशिक बादल",
      noWeatherData: "उपलब्ध छैन",
      noRainfallData: "कुनै वर्षा डाटा उपलब्ध छैन",

      realTimeMonitoringTitle: "वास्तविक समय निगरानी",
      totalStations: "कुल स्टेशनहरू",
      search: "खोज्नुहोस्",

      serialNumber: "क्र.सं.",
      basinName: "बेसिन नाम",
      stationIndex: "स्टेशन सूचकांक",
      stationName: "स्टेशन नाम",
      waterLevelM: "पानीको सतह (m)",
      warningLevelM: "चेतावनी सतह (m)",
      dangerLevelM: "खतरा सतह (m)",
      tableStatus: "स्थिति",

      loadingRainfallData: "वर्षा डाटा लोड हुँदै...",
      loadingWaterLevelData: "पानीको सतह डाटा लोड हुँदै...",
      failedToFetchRainfall:
        "वर्षा डाटा ल्याउन असफल। कृपया पछि प्रयास गर्नुहोस्।",
      failedToFetchWaterLevel:
        "पानीको सतह डाटा ल्याउन असफल। कृपया पछि प्रयास गर्नुहोस्।",
      pleaseRetryLater: "कृपया पछि प्रयास गर्नुहोस्।",
      noDataFound: "कुनै डाटा फेला परेन",
      unknown: "अज्ञात",
      unknownStation: "अज्ञात स्टेशन",

      warningNote: "नोट: वर्षाको चेतावनी स्तर (mm):",
      mmIn1hr: "१ घण्टामा ६० mm",
      mmIn3hr: "३ घण्टामा ८० mm",
      mmIn6hr: "६ घण्टामा १०० mm",
      mmIn12hr: "१२ घण्टामा १२० mm",
      mmIn24hr: "२४ घण्टामा १४० mm",

      home: "गृह",
      about: "बारेमा",
      digest: "सारांश",
      getInvolved: "सहभागी हुनुहोस्",
      login: "लगइन",
      logout: "लगआउट",

      stayUpToDate: "अद्यावधिक रहनुहोस्",
      newsletterDescription:
        "सुविधाहरू र रिलिजहरूमा अद्यावधिक रहन हाम्रो न्यूजलेटरमा सामेल हुनुहोस्।",
      enterYourEmail: "आफ्नो इमेल प्रविष्ट गर्नुहोस्",
      subscribe: "सदस्यता लिनुहोस्",
      technicalPartner: "प्राविधिक साझेदार",
      designAssociatesNepal: "डिजाइन एसोसिएट्स नेपाल",
    },
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: "en",
    debug: false,

    detection: {
      order: ["localStorage", "navigator", "htmlTag"],
      caches: ["localStorage"],
    },

    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
