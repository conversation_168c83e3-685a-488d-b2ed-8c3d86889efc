import {
  init,
  captureException,
  captureMessage,
  setUser,
  setTag,
  setContext,
  withScope,
} from "@sentry/react";

export const initSentry = () => {
  init({
    dsn:
      import.meta.env.VITE_SENTRY_DSN ||
      "https://<EMAIL>/4509699705929808",

    environment: import.meta.env.MODE || "development",

    release: import.meta.env.VITE_VERSION || "1.0.0",

    tracesSampleRate: 0,

    replaysSessionSampleRate: 0,
    replaysOnErrorSampleRate: 0,

    beforeSend(event, hint) {
      const error = hint.originalException as Error | undefined;

      if (
        import.meta.env.MODE === "development" &&
        error?.name === "NetworkError"
      ) {
        return null;
      }

      if (
        error?.name === "AbortError" ||
        error?.message?.includes("cancelled")
      ) {
        return null;
      }

      return event;
    },

    initialScope: {
      tags: {
        component: "get-prepare-frontend",
      },
    },
  });
};

export const setSentryUser = (user: {
  id?: string;
  email?: string;
  username?: string;
  [key: string]: unknown;
}) => {
  setUser(user);
};

export const captureError = (
  error: Error,
  context?: Record<string, Record<string, unknown>>,
) => {
  withScope((scope) => {
    if (context) {
      Object.keys(context).forEach((key) => {
        scope.setContext(key, context[key]);
      });
    }
    captureException(error);
  });
};

export const captureSentryMessage = (
  message: string,
  level?: "fatal" | "error" | "warning" | "log" | "info" | "debug",
  context?: Record<string, Record<string, unknown>>,
) => {
  withScope((scope) => {
    if (context) {
      Object.keys(context).forEach((key) => {
        scope.setContext(key, context[key]);
      });
    }
    captureMessage(message, level || "info");
  });
};

export const setSentryTag = (key: string, value: string) => {
  setTag(key, value);
};

export const setSentryContext = (
  key: string,
  context: Record<string, unknown>,
) => {
  setContext(key, context);
};
