import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface PaginationParams {
  page?: number;
  page_size?: number;
}

export interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export function createPaginationState(
  response: PaginatedResponse<any>,
  currentPage: number,
  pageSize: number
): PaginationState {
  const totalPages = Math.ceil(response.count / pageSize);

  return {
    currentPage,
    pageSize,
    totalItems: response.count,
    totalPages,
    hasNext: !!response.next,
    hasPrevious: !!response.previous,
  };
}

export async function fetchAllPages<T>(
  fetchFunction: (params: PaginationParams) => Promise<PaginatedResponse<T>>,
  pageSize: number = 50,
  onProgress?: (progress: { current: number; total: number; data: T[] }) => void
): Promise<T[]> {
  const allData: T[] = [];
  let currentPage = 1;
  let totalPages = 1;

  try {
    const firstResponse = await fetchFunction({ page: currentPage, page_size: pageSize });
    allData.push(...firstResponse.results);
    totalPages = Math.ceil(firstResponse.count / pageSize);

    if (onProgress) {
      onProgress({ current: 1, total: totalPages, data: [...allData] });
    }

    const remainingRequests: Promise<PaginatedResponse<T>>[] = [];
    for (let page = 2; page <= totalPages; page++) {
      remainingRequests.push(fetchFunction({ page, page_size: pageSize }));
    }

    if (remainingRequests.length > 0) {
      const remainingResponses = await Promise.all(remainingRequests);

      remainingResponses.forEach((response, index) => {
        allData.push(...response.results);

        if (onProgress) {
          onProgress({
            current: index + 2,
            total: totalPages,
            data: [...allData]
          });
        }
      });
    }

    return allData;
  } catch (error) {
    console.error('Error fetching paginated data:', error);
    throw error;
  }
}

export function buildPaginationQuery(params: PaginationParams): string {
  const searchParams = new URLSearchParams();

  if (params.page !== undefined) {
    searchParams.append('page', params.page.toString());
  }

  if (params.page_size !== undefined) {
    searchParams.append('page_size', params.page_size.toString());
  }

  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : '';
}
