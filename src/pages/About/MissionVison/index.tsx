export default function MissionVision() {
  return (
    <section
      id="mission-vision"
      className="relative overflow-hidden px-4 py-20 text-white md:p-[7.5rem]"
      style={{
        backgroundImage:
          "linear-gradient(to right, #1D8E89, #1E3C5A), url('/BG.svg')",
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center",
        backgroundBlendMode: "overlay",
      }}
    >
      <div className="flex flex-col gap-20 text-center md:flex-row md:justify-between">
        <div className="flex w-full flex-col items-center justify-center md:w-1/2">
          <img src="/mission.svg" className="mb-3 h-14 w-14 md:h-24 md:w-24" />
          <h3 className="poppins-bold mb-10 inline-block text-3xl text-white after:mx-auto after:mt-2 after:block after:h-[3px] after:w-32 after:bg-white md:text-[3.5rem]">
            Mission
          </h3>
          <p className="poppins-regular text-sm text-[#F4F4F4] md:text-xl">
            To democratize disaster and climate risk information, empower
            locally-led resilience, and advance anticipatory action across Nepal
            and beyond.
          </p>
        </div>

        <div className="flex w-full flex-col items-center justify-center md:w-1/2">
          <img src="/vision.svg" className="mb-3 h-14 w-14 md:h-24 md:w-24" />
          <h3 className="poppins-bold mb-10 inline-block text-3xl text-white after:mx-auto after:mt-2 after:block after:h-[3px] after:w-32 after:bg-white md:text-[3.5rem]">
            Vision
          </h3>
          <p className="poppins-regular flex text-center text-sm text-[#F4F4F4] md:text-xl">
            To create a future where every sectors, no matter how vulnerable, is
            fully prepared for climate change and disasters through localized
            knowledge, inclusive planning, and early action systems.
          </p>
        </div>
      </div>
    </section>
  );
}
