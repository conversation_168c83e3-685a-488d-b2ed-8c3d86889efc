import { useState } from "react";

const timelineData = [
  {
    date: "Nov 2024",
    title: "Participant",
    description:
      "Participate in Youth Climate Initiative Programme - Idea Labs",
    year: "2024",
  },
  {
    date: "Dec 2024",
    title: "Winner",
    description: "Winner of the Youth Climate Initiative Programme-Idea Labs",
    year: "2024",
  },
  {
    date: "Apr 2025",
    title: "Incubation",
    description: "Incubation Phase Youth Climate Innovation Program",
    year: "2025",
  },
];

const years = ["All", "2024", "2025"];

export default function Timeline() {
  const [selectedYear, setSelectedYear] = useState("All");

  const filteredData =
    selectedYear === "All"
      ? timelineData
      : timelineData.filter((item) => item.year === selectedYear);

  return (
    <section className="bg-white px-6 py-20 md:px-[7.5rem] md:py-[7.5rem]">
      <div className="mb-8 text-center">
        <h2 className="section-title poppins-bold mb-5 leading-[120%]">
          Timeline
        </h2>
        <p className="poppins-regular text-xl text-gray-600">
          See our journey from the beginning
        </p>

        <div className="mt-[3.5rem] mb-[6.25rem] flex justify-center gap-2">
          {years.map((year) => (
            <button
              key={year}
              onClick={() => setSelectedYear(year)}
              className={`inter-semibold rounded-md border px-8 py-2 text-sm ${
                selectedYear === year
                  ? "bg-[#02475C] text-white"
                  : "bg-[#E6EDEF] text-[#7B7B7B]"
              }`}
            >
              {year}
            </button>
          ))}
        </div>
      </div>

      <div className="relative">
        <div className="hidden flex-col items-center md:flex">
          <div className="relative mb-16 h-1 w-full bg-[#667085]">
            {filteredData.map((_, i) => (
              <div
                key={i}
                className="absolute -top-2.5 h-5 w-5 rounded-full border-4 border-gray-200 bg-[#1E918C]"
                style={{
                  left: `${(i / filteredData.length) * 100}%`,
                  transform: "translateX(-60%)",
                }}
              />
            ))}
          </div>

          <div className="flex w-full gap-20">
            {filteredData.map((item, i) => (
              <div key={i} className="w-1/4 text-start">
                <p className="poppins-regular text-base text-[#02475C]">
                  {item.date}
                </p>
                <h4 className="poppins-semibold mt-2 text-lg text-gray-800">
                  {item.title}
                </h4>
                <p className="poppins-regular mt-1 max-w-60 text-sm text-gray-800">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        <div className="relative flex flex-col gap-[3.5rem] border-l-6 border-[#667085] pl-10 md:hidden">
          {filteredData.map((item, i) => (
            <div key={i} className="relative">
              <span className="absolute top-1.5 -left-13 h-5 w-5 rounded-full border-4 border-gray-200 bg-[#1E918C]" />
              <p className="poppins-regular text-base text-[#02475C]">
                {item.date}
              </p>
              <h4 className="poppins-semibold mt-1 text-lg text-gray-800">
                {item.title}
              </h4>
              <p className="poppins-regular mt-1 max-w-60 text-sm text-gray-800">
                {item.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
