import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Link } from "react-router-dom";

interface Article {
  slug: string;
  date: string;
  title: string;
  summary: string;
  image: string;
}

export const BlogCard: React.FC<{ article: Article }> = ({ article }) => {
  return (
    <Card className="overflow-hidden rounded-3xl border border-gray-200 text-[#02475C] shadow-sm">
      <img
        src={article.image}
        alt={article.title}
        className="h-72 w-full object-cover object-left"
      />

      <CardContent className="p-5">
        <p className="poppins-regular text-sm text-[#475467] md:text-base">
          {article.date}
        </p>
        <h3 className="poppins-bold mt-2 text-xl leading-snug text-[#02475C] md:mt-5">
          {article.title.length > 60
            ? article.title.slice(0, 60) + "..."
            : article.title}
        </h3>
        <p className="poppins-regular mt-3 text-sm leading-relaxed text-[#02475C] md:text-lg">
          {article.summary.length > 100
            ? article.summary.slice(0, 100) + "..."
            : article.summary}
        </p>
        <Link
          to={`/blogs/${article.slug}`}
          className="poppins-semibold mt-3 inline-block text-sm hover:underline md:text-[1.1313rem]"
        >
          Read more →
        </Link>
      </CardContent>
    </Card>
  );
};
