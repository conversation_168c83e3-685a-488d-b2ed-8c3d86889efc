import { Link, useParams } from "react-router-dom";
import { articles } from "@/constants";
import { BlogCard } from "@/pages/Blogs/BlogCard";
import Navbar from "../LandingPage/NavBar";
import Footer from "../Footer";

export default function NewsDetail() {
  const { slug } = useParams<{ slug: string }>();
  const article = articles.find((a) => a.slug === slug);

  if (!article) return <p>Article not found</p>;

  // const similarArticles = articles.filter((a) => a.slug !== slug).slice(0, 3);

  return (
    <>
      <Navbar />
      <section className="px-4 md:px-16 lg:px-32">
        <div className="mx-auto max-w-screen-xl">
          <button className="poppins-semibold my-6 text-[1.1313rem] text-[#02475C]">
            <Link to="/news" className="flex items-center justify-center">
              <span className="material-symbols-outlined z-10 mr-2">
                chevron_left
              </span>
              All News
            </Link>
          </button>

          <div className="relative">
            <img
              src={article.image}
              alt={article.title}
              className="hidden w-full object-cover md:block"
            />
          </div>

          <div className="relative z-10 mx-auto w-full bg-white p-4 md:-mt-[6rem] md:w-[70%] md:p-[3.75rem]">
            <h1 className="poppins-bold text-[1.5rem] leading-[120%] text-[#02475C] md:text-[2.5rem]">
              {article.title}
            </h1>
            <p className="poppins-semibold mb-4 flex items-center gap-2 text-base text-[#02475C] md:my-7">
              <img src="/icon.svg" alt="Calendar Icon" className="h-5 w-5" />

              {article.date}
            </p>
            <div className="poppins-regular mb-12 text-sm leading-[120%] whitespace-pre-line text-[#475467] md:text-lg md:leading-relaxed">
              {article.content}
            </div>

            <div className="flex flex-row items-start gap-4 border-t pt-8 md:gap-6">
              <p className="poppins-regular text-lg text-[#02475C]">Share</p>
              <div className="flex gap-6">
                <a href="#" className="hover:opacity-70">
                  <img src="/fb.svg" alt="Facebook" className="h-6 w-6" />
                </a>
                <a href="#" className="hover:opacity-70">
                  <img src="/twitter.svg" alt="Twitter" className="h-6 w-6" />
                </a>
                <a href="#" className="hover:opacity-70">
                  <img src="/linkedln.svg" alt="LinkedIn" className="h-6 w-6" />
                </a>
              </div>
            </div>
          </div>
        </div>

        <div className="mx-auto my-20 max-w-screen-xl">
          <h2 className="section-title poppins-bold mb-[5.5rem]">
            Similar News
          </h2>
          {/* <div className="grid grid-cols-1 gap-10 md:grid-cols-2 md:gap-6 lg:grid-cols-3">
            {similarArticles.map((a) => (
              <BlogCard key={a.slug} article={a} />
            ))}
          </div> */}
          <div className="grid max-w-screen-xl grid-cols-1 gap-5 md:grid-cols-2 lg:grid-cols-3">
            {articles.slice(0, 3).map((article) => (
              <BlogCard key={article.slug} article={article} />
            ))}
          </div>
        </div>
      </section>
      <Footer />
    </>
  );
}
