 
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import Footer from "../Footer";
import Navbar from "../LandingPage/NavBar";
import { articles } from "@/constants";
import { BlogCard } from "@/pages/Blogs/BlogCard";

export default function NewsAndStories() {
  return (
    <>
      <Navbar />
      <section className="px-4 py-10 md:px-16 md:py-20 lg:px-30">
        <div className="mb-6 text-center md:mb-[5.5rem]">
          <h2 className="section-title poppins-bold mb-2">News & Stories</h2>
          <p className="poppins-semibold mx-auto mt-2 max-w-3xl text-base leading-[120%] text-[#475467] md:text-xl">
            Stay updated with the latest news and insights on climate change,
            social impact and technology.
          </p>
        </div>

        <Tabs
          defaultValue="news"
          className="flex w-full items-center justify-center"
        >
          <TabsList className="poppins-regular mb-[5.5rem] flex justify-center gap-1 md:flex-wrap md:gap-4">
            <TabsTrigger value="news">News</TabsTrigger>
            <TabsTrigger value="press">Press Releases</TabsTrigger>
            <TabsTrigger value="reports">Resources</TabsTrigger>
            <TabsTrigger value="videos">Videos</TabsTrigger>
          </TabsList>

          <TabsContent value="news">
            <div className="grid max-w-screen-xl grid-cols-1 gap-5 md:grid-cols-2 lg:grid-cols-3">
              {articles.slice(0, 3).map((article) => (
                <BlogCard key={article.slug} article={article} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="press">
            <p className="text-center text-gray-500">No press releases yet.</p>
          </TabsContent>
          <TabsContent value="reports">
            <p className="text-center text-gray-500">
              No reports or resources yet.
            </p>
          </TabsContent>
          <TabsContent value="videos">
            <p className="text-center text-gray-500">No videos uploaded yet.</p>
          </TabsContent>
        </Tabs>
      </section>
      <Footer />
    </>
  );
}
