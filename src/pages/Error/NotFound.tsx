import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

export default function NotFound() {
  const navigate = useNavigate();

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-[#F5F5F5] px-4">
      <div className="text-center">
        <h1 className="mb-2 text-9xl font-bold text-[#02475C]">404</h1>
        <h2 className="mb-4 text-2xl font-semibold text-[#434343]">
          Page Not Found
        </h2>
        <p className="mb-8 text-[#7B7B7B]">
          The page you are looking for doesn't exist or has been moved.
        </p>
        <div className="flex flex-col items-center justify-center gap-4 sm:flex-row sm:gap-6">
          <Button
            onClick={() => navigate(-1)}
            variant="outline"
            className="border-[#02475C] text-[#02475C] hover:bg-[#02475C] hover:text-white"
          >
            Go Back
          </Button>
          <Button
            onClick={() => navigate("/")}
            className="bg-[#02475C] text-white hover:bg-[#02475C]/90"
          >
            Return Home
          </Button>
        </div>
      </div>
    </div>
  );
}
