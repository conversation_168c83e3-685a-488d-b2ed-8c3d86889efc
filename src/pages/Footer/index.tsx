import { useTranslation } from "react-i18next";

export default function Footer() {
  const { t } = useTranslation();

  return (
    <footer className="bg-gradient-to-l from-[#1D8E89] to-[#1E3C5A] px-4 py-16 md:px-10 lg:px-28">
      <div className="mx-auto mb-14 flex max-w-7xl flex-col items-start justify-between gap-12 lg:flex-row lg:items-center">
        <div className="flex flex-col gap-8">
          <a href="/" className="flex items-center">
            <img src="/footer.svg" alt="Prepare Logo" className="h-10" />
          </a>
          <div className="flex gap-4">
            {/* <a
              href="https://www.instagram.com/getprepare/"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img src="/insta.svg" alt="Instagram" className="h-8" />
            </a> */}
            <a
              href="https://www.linkedin.com/company/getprepare/"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img src="/linkedin.svg" alt="LinkedIn" className="h-8" />
            </a>
          </div>
        </div>

        <div className="w-full text-white lg:w-auto">
          <p className="inter-semibold mb-2 text-xl">{t("stayUpToDate")}</p>
          <p className="poppins-regular mb-4 max-w-md text-sm">
            {t("newsletterDescription")}
          </p>
          <div className="flex flex-col gap-4 sm:flex-row sm:gap-6">
            <input
              type="email"
              placeholder={t("enterYourEmail")}
              className="poppins-regular w-full rounded-xl border-1 border-white px-4 py-2 text-base placeholder-[#FFFFFF99] sm:w-auto"
            />
            <button className="poppins-regular w-fit rounded-full border-1 border-white px-6 py-3.5 text-white transition-colors duration-300 hover:bg-white hover:text-[#07152C]">
              {t("subscribe")}
            </button>
          </div>
        </div>
      </div>

      <div className="mb-6 border-t border-[#D9DBE9]" />

      <div className="mx-auto flex max-w-7xl flex-col items-center justify-between gap-4 text-sm sm:flex-row sm:gap-0">
        <span className="inter text-white">© 2025 PREPARE</span>
        <div className="flex items-center gap-2.5">
          <a href="#" className="poppins-regular text-white">
            {t("technicalPartner")}
          </a>
          <span className="text-white">|</span>
          <a href="#" className="poppins-bold text-white">
            {t("designAssociatesNepal")}
          </a>
        </div>
      </div>
    </footer>
  );
}
