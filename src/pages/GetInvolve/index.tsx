import { roles } from "@/constants";
import Navbar from "@/pages/LandingPage/NavBar";
import Footer from "@/pages/Footer";
import { Link } from "react-router-dom";

export default function GetInvolvedSection() {
  return (
    <>
      <Navbar />
      <section
        id="getInvolved"
        className="bg-white py-10 text-center md:my-16 md:py-20"
      >
        <h2 className="poppins-bold section-title mb-5">Get Involved</h2>
        <p className="poppins-semibold text-xl leading-[120%] text-[#475467]">
          Join us to shape the future <br />
          Get involved as
        </p>
        <div className="mt-10 grid grid-cols-1 gap-6 px-4 sm:grid-cols-2 md:mt-22 md:px-[7.5rem]">
          {roles.map(
            (role: { title: string; description: string }, index: number) => (
              <Link
                to="/#contact"
                key={index}
                className="rounded-3xl border-[0.125rem] border-[#02475C] p-6 text-start shadow-sm transition duration-300 hover:shadow-md md:px-12 md:py-14"
              >
                <p className="poppins-bold mb-8 text-start text-[1.5rem] text-[#02475C] lg:text-[2rem]">
                  {role.title}
                </p>
                <p className="poppins-regular text-sm text-[#475467] md:text-xl">
                  {role.description}
                </p>
              </Link>
            ),
          )}
        </div>
      </section>
      <Footer />
    </>
  );
}
