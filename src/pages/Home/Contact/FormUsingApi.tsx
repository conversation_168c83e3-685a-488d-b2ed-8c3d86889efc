import { useState } from "react";
import PhoneInputLib from "react-phone-input-2";

import axios from "axios";
import { Toaster, toast } from "sonner";

export default function FormRestApi() {
  const [phone, setPhone] = useState("");
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [message, setMessage] = useState("");
  const [organization, setOrganization] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const serviceId = import.meta.env.VITE_EMAILJS_SERVICE_ID;
    const templateId = import.meta.env.VITE_EMAILJS_TEMPLATE_ID;
    const publicKey = import.meta.env.VITE_EMAILJS_PUBLIC_KEY;

    const data = {
      service_id: serviceId,
      template_id: templateId,
      user_id: publicKey,
      template_params: {
      full_name: name,
      to_name: "Oplus Lab",
      email: email,
      phone: phone,
      organization: organization,
      message: message,
      },
    };

    try {
      await axios.post(
        "https://api.emailjs.com/api/v1.0/email/send",
        data,
      );
      toast.success("Your message has been sent successfully!");
    } catch (error) {
      toast.error("Failed to send message. Please try again.");
    }
  };
  return (
    <div className="w-full px-2 py-10 sm:px-4 md:px-20">
      <Toaster position="top-right" richColors />
      <h3 className="poppins-bold mb-6 text-2xl text-[#02475C] md:text-[2rem]">
        Send us a message
      </h3>
      <form
        onSubmit={handleSubmit}
        className="poppins-semibold flex flex-col gap-4"
      >
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label className="poppins-semibold mb-1 block text-sm text-gray-700">
              Full Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              placeholder="Enter your name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full rounded-md border border-gray-300 px-4 py-2 text-sm focus:border-[#02475C] focus:ring-1 focus:ring-[#02475C] focus:outline-none"
            />
          </div>
          <div>
            <label className="poppins-semibold mb-1 block text-sm text-gray-700">
              Email <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full rounded-md border border-gray-300 px-4 py-2 text-sm focus:border-[#02475C] focus:ring-1 focus:ring-[#02475C] focus:outline-none"
            />
          </div>
        </div>

        {/* Organization */}
        <div>
          <label className="poppins-semibold mb-1 block text-sm text-gray-700">
            Organization
          </label>
          <input
            type="text"
            placeholder="Enter Organization"
            value={organization}
            onChange={(e) => setOrganization(e.target.value)}
            className="w-full rounded-md border border-gray-300 px-4 py-2 text-sm focus:border-[#02475C] focus:ring-1 focus:ring-[#02475C] focus:outline-none"
          />
        </div>

        {/* Phone */}

        <div>
          <label className="poppins-semibold mb-1 block text-sm text-gray-700">
            Phone <span className="text-red-500">*</span>
          </label>
          <PhoneInputLib
            country={"np"}
            value={phone}
            onChange={setPhone}
            enableSearch
            disableCountryCode={false}
            disableSearchIcon
            inputProps={{
              required: true,
              name: "phone",
              placeholder: "Enter contact detail",
            }}
            inputClass="!w-full !h-10 !text-sm !ps-14 !pe-3 !py-1.5 !border !border-gray-300 !rounded-md focus:!border-[#02475C] focus:!ring-1 focus:!ring-[#02475C] focus:!outline-none"
            containerClass="!w-full"
            buttonClass="!border-none !bg-transparent !ps-2 !me-2"
            dropdownClass="!w-72 !text-sm"
          />
        </div>

        {/* Message */}
        <div>
          <label className="poppins-semibold mb-1 block text-sm text-gray-700">
            Message <span className="text-red-500">*</span>
          </label>
          <textarea
            rows={4}
            placeholder="Enter message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="w-full rounded-md border border-gray-300 px-4 py-2 text-sm focus:border-[#02475C] focus:ring-1 focus:ring-[#02475C] focus:outline-none"
          ></textarea>
        </div>

        {/* Submit */}
        <button
          type="submit"
          className="poppins-regular mt-4 self-end rounded-full bg-gradient-to-l from-[#02475C] to-[#1D8E89] px-6 py-3 text-sm text-white transition hover:opacity-90"
        >
          Send
        </button>
      </form>
    </div>
  );
}
