import Form from "./Form";
// import FormRestApi from "./FormUsingApi";
// import FormReact from "./FormReact";

export default function Contact() {
  return (
    <section
      id="contact"
      className="bg-white px-6 py-10 md:my-24 md:px-12 md:py-24 lg:px-[7.5rem]"
    >
      <div className="mx-auto flex max-w-7xl flex-col md:h-[34rem] md:flex-row">
        <div
          className="relative flex w-full flex-col justify-between rounded-2xl p-8 text-white md:p-10"
          style={{
            backgroundImage:
              "linear-gradient(to right, #1D8E89, #02475C), url('/BG.svg')",
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "center",
            backgroundBlendMode: "overlay",
          }}
        >
          <div className="flex flex-col items-start">
            <h2 className="poppins-bold text-[2rem]">let’s talk</h2>
            <p className="poppins-regular mt-2 text-lg">
              You can contact us here
            </p>
          </div>
          <div className="poppins-regular flex flex-col gap-5 text-lg">
            <a href="tel:+9779813182094 " className="flex items-center gap-2">
              <span className="material-symbols-outlined">call</span>
              <span>+977 9813182094 </span>
            </a>

            <a
              href="mailto:<EMAIL>"
              className="flex items-center gap-2"
            >
              <span className="material-symbols-outlined">mail</span>
              <span><EMAIL></span>
            </a>

            <div className="flex items-center gap-2">
              <span className="material-symbols-outlined">location_on</span>
              <span>Kathmandu,Nepal</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex gap-4">
              {/* <img src="/insta.svg" alt="Instagram" className="h-8" /> */}
              <a
                href="https://www.linkedin.com/company/getprepare/"
                target="_blank"
                rel="noopener noreferrer"
              >
                <img src="/linkedin.svg" alt="LinkedIn" className="h-8" />
              </a>
              <a
                href="https://wa.me/9813182094 "
                target="_blank"
                rel="noopener noreferrer"
              >
                <img src="/Whatsapp.svg" alt="whatsapp" className="h-8" />
              </a>
            </div>
          </div>
          <img
            src="/LetterSend.svg"
            alt="Send"
            className="absolute right-0 bottom-0 h-32 w-3xs"
          />
        </div>

        <Form />
        {/* <FormReact /> */}
        {/* <FormRestApi /> */}
      </div>
    </section>
  );
}
