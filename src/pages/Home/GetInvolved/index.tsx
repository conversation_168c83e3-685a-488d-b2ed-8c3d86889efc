// import { roles } from "@/constants";

// export default function GetInvolvedSection() {
//   return (
//     <section id="getInvolved" className="bg-white py-20 text-center">
//       <h2 className="poppins-bold section-title">Get Involved As</h2>
//       <div className="mx-auto mt-14 grid max-w-6xl grid-cols-1 gap-6 px-4 sm:grid-cols-2">
//         {roles.map(
//           (role: { title: string; description: string }, index: number) => (
//             <div
//               key={index}
//               className="rounded-3xl border-[0.125rem] border-[#02475C] p-14 text-start shadow-sm transition duration-300 hover:shadow-md"
//             >
//               <a href="#contact" className="poppins-bold mb-8 text-start text-sm whitespace-nowrap text-[#02475C] md:text-[1.5rem] lg:text-xl lg:text-[2rem]">
//                 {role.title}
//               </a>
//               <p className="poppins-regular text-sm text-[#02475C] md:text-xl">
//                 {role.description}
//               </p>
//             </div>
//           ),
//         )}
//       </div>
//     </section>
//   );
// }
