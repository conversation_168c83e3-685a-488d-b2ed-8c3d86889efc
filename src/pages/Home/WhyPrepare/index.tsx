// import {
//   Carousel,
//   CarouselContent,
//   CarouselItem,
// } from "@/components/ui/carousel";
// import Autoplay from "embla-carousel-autoplay";
// import { useEffect, useRef, useState } from "react";

// const data = [
//   {
//     title: "The Challenge",
//     description:
//       "Climate-induced disasters are becoming more frequent, intense, and unpredictable. Vulnerable communities, especially in developing regions like Nepal, face the greatest risks—yet have the least access to localized early warnings and preparedness tools.",
//     image: "/Landslides.svg",
//   },
//   {
//     title: "The Gap",
//     description:
//       "Disaster management systems often prioritize response over prevention. Most tools and information are generalized, outdated, or not accessible to households who need it most.",
//     image: "/Landslides.svg",
//   },
//   {
//     title: "Our Solution",
//     description:
//       "PREPARE bridges this gap by offering Household & community risk assessments, digital early action protocols, real-time, hyperlocal forecasts and alerts, and youth-led engagement for climate resilience.",
//     image: "/Landslides.svg",
//   },
// ];

// export default function WhyPrepare() {
//   const plugin = useRef(Autoplay({ delay: 3500, stopOnInteraction: false }));
//   const [embla, setEmbla] = useState<any>(null);
//   const [current, setCurrent] = useState(0);

//   // useEffect(() => {
//   //   if (!embla) return;
//   //   const onSelect = () => setCurrent(embla.selectedScrollSnap());
//   //   embla.on("select", onSelect);
//   //   onSelect();
//   // }, [embla]);

//   const scrolledRef = useRef(false);

//   useEffect(() => {
//     if (!embla) return;

//     const onSelect = () => {
//       const selected = embla.selectedScrollSnap();
//       setCurrent(selected);

//       if (selected === data.length - 1 && !scrolledRef.current) {
//         scrolledRef.current = true;
//         setTimeout(() => {
//           const next = document.getElementById("digital");
//           next?.scrollIntoView({ behavior: "smooth" });
//         }, 3500);
//       }

//       if (selected !== data.length - 1) {
//         scrolledRef.current = false;
//       }
//     };

//     embla.on("select", onSelect);
//     onSelect();

//     return () => embla.off("select", onSelect);
//   }, [embla]);

//   return (
//     <section
//       id="whyPrepare"
//       className="relative bg-white px-6 py-12 sm:px-8 md:my-20 md:px-12 md:py-20 lg:px-20 xl:px-[120px]"
//     >
//       <h2 className="section-title poppins-bold mb-16">Why PREPARE?</h2>

//       <div className="relative mx-auto max-w-7xl">
//         <Carousel
//           plugins={[plugin.current]}
//           orientation="vertical"
//           className="mx-auto w-full bg-[#F0F1F1] px-4 py-6 sm:px-6 sm:py-8 md:rounded-xl md:px-10 md:py-16"
//           setApi={setEmbla}
//         >
//           <CarouselContent className="-mt-4 h-[30rem]">
//             {data.map((item, index) => (
//               <CarouselItem key={index} className="mt-4 basis-full">
//                 <div className="flex flex-col md:flex-row md:items-center">
//                   <div className="w-full md:w-1/2">
//                     <img
//                       src={item.image}
//                       alt={item.title}
//                       className="h-44 w-full object-cover md:h-auto md:rounded-xl"
//                     />
//                   </div>
//                   <div className="w-full px-20 md:w-1/2">
//                     <h3 className="poppins-bold mb-4 text-xl text-[#02475C] sm:text-2xl md:text-4xl">
//                       {item.title}
//                     </h3>
//                     <p className="poppins-regular text-sm text-[#475467] sm:text-base md:text-lg">
//                       {item.description}
//                     </p>
//                   </div>
//                   <div className="flex flex-col gap-2">
//                     {data.map((_, i) => (
//                       <span
//                         key={i}
//                         className={`h-2 w-2 rounded-full transition-all ${
//                           current === i ? "bg-[#02475C]" : "bg-[#D9D9D9]"
//                         }`}
//                       />
//                     ))}
//                   </div>
//                 </div>
//               </CarouselItem>
//             ))}
//           </CarouselContent>
//         </Carousel>
//       </div>
//     </section>
//   );
// }
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { useEffect, useRef, useState } from "react";

const data = [
  {
    title: "The Challenge",
    description:
      "Climate-induced disasters are becoming more frequent, intense, and unpredictable. Vulnerable communities, especially in developing regions like Nepal, face the greatest risks—yet have the least access to localized early warnings and preparedness tools.",
    image: "/Landslides.svg",
  },
  {
    title: "The Gap",
    description:
      "Disaster management systems often prioritize response over prevention. Most tools and information are generalized, outdated, or not accessible to households who need it most.",
    image: "/Landslides.svg",
  },
  {
    title: "Our Solution",
    description:
      "PREPARE bridges this gap by providing local risk assessments, digital early action plans, real-time alerts, and citizen scientists and youth-led climate resilience initiatives.",
    image: "/Landslides.svg",
  },
];

export default function WhyPrepare() {
  const plugin = useRef(Autoplay({ delay: 3500, stopOnInteraction: false }));
  const [embla, setEmbla] = useState<any>(null);
  const [current, setCurrent] = useState(0);

  const hasScrolledRef = useRef(false);
  const isWhyPrepareVisible = useRef(false);
  const isDigitalVisible = useRef(false);

  useEffect(() => {
    if (!embla) return;

    const onSelect = () => {
      const selected = embla.selectedScrollSnap();
      setCurrent(selected);

      if (
        selected === data.length - 1 &&
        isWhyPrepareVisible.current &&
        !hasScrolledRef.current &&
        !isDigitalVisible.current
      ) {
        hasScrolledRef.current = true;
        plugin.current?.stop();

        setTimeout(() => {
          const digital = document.getElementById("digital");
          if (digital) {
            digital.scrollIntoView({ behavior: "smooth" });
          }
        }, 1000);
      }
    };

    embla.on("select", onSelect);
    onSelect();

    return () => embla.off("select", onSelect);
  }, [embla]);

  useEffect(() => {
    const digital = document.getElementById("digital");
    const whyPrepare = document.getElementById("whyPrepare");

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const id = entry.target.id;
          const visible = entry.isIntersecting;

          if (id === "whyPrepare") {
            isWhyPrepareVisible.current = visible;
            if (visible) {
              hasScrolledRef.current = false;
              plugin.current?.play();
            } else {
              plugin.current?.stop();
            }
          }

          if (id === "digital") {
            isDigitalVisible.current = visible;
            if (visible) {
              plugin.current?.stop();
            }
          }
        });
      },
      { threshold: 0.6 },
    );

    if (whyPrepare) observer.observe(whyPrepare);
    if (digital) observer.observe(digital);

    return () => {
      if (whyPrepare) observer.unobserve(whyPrepare);
      if (digital) observer.unobserve(digital);
    };
  }, []);

  return (
    <section
      id="whyPrepare"
      className="relative bg-white px-6 py-12 sm:px-8 md:my-20 md:px-12 md:py-20 lg:px-20 xl:px-[120px]"
    >
      <h2 className="section-title poppins-bold mb-16">Why PREPARE?</h2>

      <div className="relative mx-auto max-w-7xl">
        <Carousel
          plugins={[plugin.current]}
          orientation="vertical"
          className="mx-auto w-full bg-[#F0F1F1] px-4 py-6 sm:px-6 sm:py-8 md:rounded-xl md:px-10 md:py-16"
          setApi={setEmbla}
        >
          <CarouselContent className="-mt-4 h-[30rem]">
            {data.map((item, index) => (
              <CarouselItem key={index} className="mt-4 basis-full">
                <div className="flex flex-col gap-5 md:flex-row md:items-center md:gap-0">
                  <div className="w-full md:w-1/2">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="h-44 w-full object-cover md:h-auto md:rounded-xl"
                    />
                  </div>
                  <div className="w-full px-5 md:w-1/2 md:px-20">
                    <h3 className="poppins-bold mb-4 text-xl text-[#02475C] sm:text-2xl md:text-4xl">
                      {item.title}
                    </h3>
                    <p className="poppins-regular text-sm leading-[120%] text-[#475467] sm:text-base md:text-lg md:leading-normal">
                      {item.description}
                    </p>
                  </div>
                  <div className="flex flex-col gap-2">
                    {data.map((_, i) => (
                      <span
                        key={i}
                        className={`h-2 w-2 rounded-full transition-all ${
                          current === i ? "bg-[#02475C]" : "bg-[#D9D9D9]"
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </section>
  );
}
