import Digital from "./Digital";
import Footer from "@/pages/Footer";
// import GetInvolvedSection from "./GetInvolved";
import HeroSection from "@/pages/LandingPage/Hero";
import Navbar from "@/pages/LandingPage/NavBar";

import PartnersSection from "./PartnerSection";
import PrepareIntroSection from "./Prepare";
import WhyPrepare from "./WhyPrepare";
import Contact from "./Contact";
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import ScrollToTopOnRouteUpdate from "@/_lib/ScrollToTop";

function App() {
  const location = useLocation();
  useEffect(() => {
    if (location.hash === "#contact") {
      const contactSection = document.getElementById("contact");
      if (contactSection) {
        contactSection.scrollIntoView({ behavior: "smooth" });
        <ScrollToTopOnRouteUpdate />;
      }
    }
  }, [location]);
  return (
    <>
      <Navbar />
      <HeroSection />
      <PrepareIntroSection />
      <WhyPrepare />
      <Digital />

      <PartnersSection />
      {/* <GetInvolvedSection /> */}
      <Contact />
      <Footer />
    </>
  );
}

export default App;
