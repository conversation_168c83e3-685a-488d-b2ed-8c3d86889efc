export default function HeroSection() {
  return (
    <section id="hero" className="bg-white px-6 py-20 text-center">
      <h1 className="section-title poppins-bold">Anticipate. Act. Adapt.</h1>

      <p className="poppins-semibold mx-auto mb-6 max-w-xl text-lg leading-normal text-[#02475C] md:text-xl">
        Welcome to PREPARE – A platform for data-driven early action and
        community-powered climate resilience.
      </p>

      <button
        onClick={() => {
          document
            .getElementById("contact")
            ?.scrollIntoView({ behavior: "smooth" });
        }}
        className="poppins-regular cursor-pointer rounded-full bg-gradient-to-l from-[#02475C] to-[#1D8E89] px-6 py-3 text-xl text-white transition hover:opacity-90"
      >
        Let's Talk
      </button>

      {/* <a
        href="#contact"
        className="poppins-semibold inline-block cursor-pointer rounded-full bg-gradient-to-l from-[#02475C] to-[#1D8E89] px-6 py-3 text-xl text-white transition hover:opacity-90"
      >
        Let's Talk
      </a> */}

      <div className="mt-14 flex justify-center">
        <img
          src="/iPad.svg"
          alt="Disaster preview"
          className="w-full max-w-4xl rounded-2xl"
        />
      </div>
    </section>
  );
}
