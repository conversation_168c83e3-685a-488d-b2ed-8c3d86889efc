import { useState } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import LoginDialog from "@/components/LoginDialog";
import { useAuth } from "@/contexts/AuthContext";
import { useTranslation } from "react-i18next";
import LanguageTranslator from "@/components/LanguageTranslator";

export default function Navbar() {
  const [menuOpen, setMenuOpen] = useState(false);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const { isAuthenticated, logout } = useAuth();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const navItems = [
    { labelKey: "home", to: "/", icon: "home" },
    { labelKey: "about", to: "/about", icon: "info" },
    { labelKey: "digest", to: "/news", icon: "newspaper" },
    { labelKey: "getInvolved", to: "/getinvolved", icon: "handshake" },
  ];

  const dashboardItem = {
    labelKey: "dashboard",
    to: "/risk/dashboard",
    icon: "dashboard",
  };

  const handleLogout = () => {
    logout();
    setMenuOpen(false);
    navigate("/");
  };

  return (
    <nav className="sticky top-0 z-50 w-full bg-white px-6 shadow-sm md:px-10 lg:px-[7.5rem]">
      <div className="mx-auto grid max-w-7xl grid-cols-12 items-center py-4">
        <div className="col-span-3 flex items-center">
          <a href="/" className="flex items-center">
            <img src="/PrepareLogo.svg" alt="Prepare Logo" className="h-10" />
          </a>
        </div>

        <ul className="poppins-semibold col-span-6 hidden items-center justify-center whitespace-nowrap text-[#0D1C2E] md:flex md:text-sm lg:text-[1.125rem]">
          {navItems.map((item) => (
            <li key={item.labelKey}>
              <NavLink
                to={item.to}
                className={({ isActive }) =>
                  isActive
                    ? "relative px-3 py-2 text-[#02475C] after:absolute after:bottom-0 after:left-1/2 after:h-1 after:w-1 after:-translate-x-1/2 after:rounded-full after:bg-[#02475C]"
                    : "relative px-3 py-2 text-gray-700 hover:text-[#02475C]"
                }
              >
                {t(item.labelKey)}
              </NavLink>
            </li>
          ))}
          {isAuthenticated && (
            <li>
              <NavLink
                to={dashboardItem.to}
                className={({ isActive }) =>
                  isActive
                    ? "relative px-3 py-2 text-[#02475C] after:absolute after:bottom-0 after:left-1/2 after:h-1 after:w-1 after:-translate-x-1/2 after:rounded-full after:bg-[#02475C]"
                    : "relative px-3 py-2 text-gray-700 hover:text-[#02475C]"
                }
              >
                {t(dashboardItem.labelKey)}
              </NavLink>
            </li>
          )}
        </ul>

        <div className="col-span-3 hidden items-center justify-end gap-3 md:flex">
          <LanguageTranslator />
          {!isAuthenticated && (
            <button
              onClick={() => setShowLoginDialog(true)}
              className="poppins-regular cursor-pointer rounded-lg bg-gradient-to-l from-[#02475C] to-[#1D8E89] px-4 py-2 text-[1.25rem] text-white transition hover:opacity-90"
            >
              {t("login")}
            </button>
          )}
          {isAuthenticated && (
            <button
              onClick={handleLogout}
              className="poppins-regular cursor-pointer rounded-lg border border-[#02475C] bg-white px-4 py-2 text-[1.1rem] text-[#02475C] transition hover:bg-[#f1f6f7]"
            >
              {t("logout")}
            </button>
          )}
        </div>

        <div className="col-span-9 flex justify-end md:hidden">
          <button
            className="ml-4 text-[#0D1C2E]"
            onClick={() => setMenuOpen(!menuOpen)}
          >
            <span className="material-symbols-outlined text-3xl">
              {menuOpen ? "close" : "menu"}
            </span>
          </button>
        </div>
      </div>

      {menuOpen && (
        <div className="fixed inset-0 z-40 overflow-y-auto bg-white p-6 md:hidden">
          <div className="mb-6 flex items-center justify-between">
            <img src="/PrepareLogo.svg" alt="Prepare Logo" className="h-10" />
            <button
              onClick={() => setMenuOpen(false)}
              className="text-[#0D1C2E]"
            >
              <span className="material-symbols-outlined text-3xl">close</span>
            </button>
          </div>

          <ul className="flex flex-col gap-4">
            {navItems.map((item) => (
              <li key={item.labelKey}>
                <NavLink to={item.to} onClick={() => setMenuOpen(false)}>
                  {({ isActive }) => (
                    <div className="flex items-center justify-between rounded-xl bg-[#F9FBFC] px-4 py-4 shadow-sm transition hover:bg-[#f1f6f7]">
                      <div className="flex items-center gap-3">
                        <span
                          className={`material-symbols-outlined text-xl ${
                            isActive ? "text-[#02475C]" : "text-[#9CA3AF]"
                          }`}
                        >
                          {item.icon}
                        </span>
                        <span
                          className={`poppins-medium text-base ${
                            isActive ? "text-[#02475C]" : "text-[#9CA3AF]"
                          }`}
                        >
                          {t(item.labelKey)}
                        </span>
                      </div>
                      <span className="material-symbols-outlined text-[#9CA3AF]">
                        arrow_forward
                      </span>
                    </div>
                  )}
                </NavLink>
              </li>
            ))}
            {isAuthenticated && (
              <li>
                <NavLink
                  to={dashboardItem.to}
                  onClick={() => setMenuOpen(false)}
                >
                  {({ isActive }) => (
                    <div className="flex items-center justify-between rounded-xl bg-[#F9FBFC] px-4 py-4 shadow-sm transition hover:bg-[#f1f6f7]">
                      <div className="flex items-center gap-3">
                        <span
                          className={`material-symbols-outlined text-xl ${
                            isActive ? "text-[#02475C]" : "text-[#9CA3AF]"
                          }`}
                        >
                          dashboard
                        </span>
                        <span
                          className={`poppins-medium text-base ${
                            isActive ? "text-[#02475C]" : "text-[#9CA3AF]"
                          }`}
                        >
                          {t(dashboardItem.labelKey)}
                        </span>
                      </div>
                      <span className="material-symbols-outlined text-[#9CA3AF]">
                        arrow_forward
                      </span>
                    </div>
                  )}
                </NavLink>
              </li>
            )}
          </ul>

          <div className="mt-6 flex flex-col gap-4">
            <div className="flex justify-center">
              <LanguageTranslator />
            </div>
            {!isAuthenticated ? (
              <button
                onClick={() => {
                  setMenuOpen(false);
                  setShowLoginDialog(true);
                }}
                className="poppins-regular w-full cursor-pointer rounded-lg bg-gradient-to-l from-[#02475C] to-[#1D8E89] px-4 py-2 text-[1.25rem] text-white transition hover:opacity-90"
              >
                {t("login")}
              </button>
            ) : (
              <button
                onClick={handleLogout}
                className="poppins-regular w-full cursor-pointer rounded-lg border border-[#02475C] bg-white px-4 py-2 text-[1.1rem] text-[#02475C] transition hover:bg-[#f1f6f7]"
              >
                {t("logout")}
              </button>
            )}
          </div>
        </div>
      )}

      <LoginDialog open={showLoginDialog} onOpenChange={setShowLoginDialog} />
    </nav>
  );
}
