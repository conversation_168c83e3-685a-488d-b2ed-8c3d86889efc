import { useNavigate } from "react-router-dom";

export default function FirstAlertLog() {
  const navigate = useNavigate();

  return (
    <div className="flex h-[calc(100vh-4rem)] flex-col items-center justify-center">
      <img src="/noalert.svg" className="mb-8" />
      <p className="lato-semibold text-xl">No Alert Log</p>
      <p className="lato-regular mb-12 text-base">
        Send your first alert by clicking button below
      </p>
      <button
        onClick={() => navigate("/risk/send-alert")}
        className="poppins-regular h-10 cursor-pointer rounded-lg border-1 border-[#02475C] bg-none px-6 py-2 text-base leading-[100%] text-[#02475C] transition hover:bg-[#E9F3F0]"
      >
        Send Alert
      </button>
    </div>
  );
}
