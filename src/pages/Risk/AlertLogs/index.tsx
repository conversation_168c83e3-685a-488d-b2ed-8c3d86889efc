import { useState } from "react";
import { Search, Calendar, Radio } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { EnhancedPagination } from "@/components/ui/pagination";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import FirstAlertLog from "./EmptyAlertLog";
import { useSendAlerts } from "@/api/sendAlert";
import { HazardType, Channel } from "@/types/sendAlert";
import { formatDistanceToNow, parseISO } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";

const getHazardTypeDisplay = (hazardType: string) => {
  return hazardType.charAt(0).toUpperCase() + hazardType.slice(1);
};

const getHazardIcon = (hazardType: string): string => {
  switch (hazardType) {
    case "fire":
      return "/fire.svg";
    case "flood":
      return "/flood.svg";
    case "drought":
      return "/drought.svg";
    case "landslide":
      return "/landslide.svg";
    default:
      return "/icon.svg";
  }
};

const getChannelIcon = (channel: Channel) => {
  return channel === Channel.SMS ? (
    <Radio className="h-3 w-3 text-[#667085]" />
  ) : (
    <span className="material-symbols-outlined text-xs text-[#667085]">
      mail
    </span>
  );
};

const getChannelDisplay = (channel: Channel) => {
  return `${channel.toUpperCase()} Alert`;
};

export default function AlertLogs() {
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const { data: alertLogs, isLoading, isError } = useSendAlerts();

  const filteredLogs =
    alertLogs?.filter((log) => {
      const matchesSearch =
        !searchQuery ||
        log.alert_message?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        log.hazard_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (log.mail_subject &&
          log.mail_subject.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesType =
        typeFilter === "all" || log.hazard_type === typeFilter;

      return matchesSearch && matchesType;
    }) || [];

  const totalItems = filteredLogs.length;
  const totalPages = Math.max(1, Math.ceil(totalItems / itemsPerPage));
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
  const currentLogs = filteredLogs.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (items: number) => {
    setItemsPerPage(items);
    setCurrentPage(1);
  };

  const formatDate = (dateString: string) => {
    try {
      let isoString = dateString;
      if (!/[zZ]|[+-]\d{2}:\d{2}$/.test(dateString)) {
        isoString += "Z";
      }
      const date = parseISO(isoString);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch {
      return dateString;
    }
  };

  const hasAlertLogs =
    !isLoading && !isError && alertLogs && alertLogs.length > 0;

  return (
    <div className="flex h-full w-full flex-col overflow-hidden bg-[#F5F5F5]">
      {isLoading ? (
        <div className="flex flex-1 flex-col gap-5 p-6">
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-40" />
            <div className="flex items-center gap-3">
              <Skeleton className="h-9 w-60" />
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>
          <div className="flex-1 rounded-lg bg-white p-2">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="mb-2">
                <Skeleton className="h-24 w-full" />
              </div>
            ))}
          </div>
        </div>
      ) : isError ? (
        <div className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600">
              Error Loading Data
            </h2>
            <p className="mt-2 text-gray-600">
              We couldn't load the alert logs. Please try again later.
            </p>
          </div>
        </div>
      ) : hasAlertLogs ? (
        <>
          <div className="flex-none p-6 pb-3">
            <div className="flex items-center justify-between">
              <h1 className="font-poppins text-xl leading-normal font-medium text-slate-900">
                Alert Logs
              </h1>

              <div className="flex items-center gap-3">
                <div className="relative h-9 w-60">
                  <Input
                    type="text"
                    placeholder="Search"
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="font-poppins h-full w-full rounded-lg border border-[#d9d9d9] bg-white pr-3 pl-10 text-sm"
                  />
                  <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                </div>

                <Select
                  value={typeFilter}
                  onValueChange={(value) => {
                    setTypeFilter(value);
                    setCurrentPage(1);
                  }}
                >
                  <SelectTrigger className="font-poppins relative h-9 w-[140px] rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                    <SelectValue
                      placeholder="Type"
                      className="text-sm font-semibold text-[#9d9d9d]"
                    />
                    <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555]">
                      arrow_drop_down
                    </span>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value={HazardType.FLOOD}>Flood</SelectItem>
                    <SelectItem value={HazardType.LANDSLIDE}>
                      Landslide
                    </SelectItem>
                    <SelectItem value={HazardType.FIRE}>Fire</SelectItem>
                    <SelectItem value={HazardType.DROUGHT}>Drought</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="flex min-h-0 flex-grow flex-col px-6 pb-3">
            <div className="flex min-h-0 flex-grow flex-col overflow-hidden rounded-lg bg-white">
              {currentLogs.length === 0 ? (
                <div className="flex flex-1 items-center justify-center p-6">
                  <p className="text-gray-500">
                    No alert logs match your filters.
                  </p>
                </div>
              ) : (
                <div className="min-h-0 flex-grow overflow-y-auto">
                  <div className="p-2">
                    {currentLogs.map((log, index) => (
                      <div key={log.id}>
                        <Card className="border-0 bg-white p-4">
                          <CardContent className="flex items-center gap-8 p-0">
                            <div className="flex flex-1 items-center gap-6">
                              <Button
                                variant="ghost"
                                className="flex h-10 w-10 items-center justify-center gap-2.5 overflow-hidden rounded-[40px] bg-[#f4f7fe] p-0"
                                aria-label={log.hazard_type}
                              >
                                <img
                                  src={getHazardIcon(log.hazard_type)}
                                  alt={log.hazard_type}
                                  className="h-full w-full object-contain"
                                />
                              </Button>

                              <div className="flex flex-1 flex-col gap-1 pb-3">
                                <h3 className="font-poppins text-[14px] leading-[20px] font-semibold text-[#0F172A]">
                                  {getHazardTypeDisplay(log.hazard_type)} Alert
                                </h3>
                                <p className="font-poppins overflow-hidden pb-1 text-[14px] leading-[20px] font-normal text-ellipsis text-[#0F172A]">
                                  {log.alert_message || "No message content"}
                                </p>

                                <div className="flex gap-4">
                                  <div className="flex items-center gap-1">
                                    <Calendar className="h-3 w-3 text-[#667085]" />
                                    <span className="font-poppins text-sm text-[#667085]">
                                      {formatDate(log.created_at)}
                                    </span>
                                  </div>

                                  <div className="flex items-center gap-2">
                                    {getChannelIcon(log.channel)}
                                    <span className="font-poppins text-sm text-[#667085]">
                                      {getChannelDisplay(log.channel)}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center gap-4">
                              <div className="flex items-center rounded-lg border border-[#e9e9e9] px-3 py-2">
                                <span className="font-poppins text-sm font-bold text-gray-700">
                                  {getHazardTypeDisplay(log.hazard_type)}
                                </span>
                              </div>
                            </div>

                            {/*  */}
                          </CardContent>
                        </Card>
                        {index < currentLogs.length - 1 && (
                          <div className="mx-4 border-b border-[#E5E7EB]" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="mx-6 mb-3 flex-none">
            <EnhancedPagination
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={itemsPerPage}
              totalItems={totalItems}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
            />
          </div>
        </>
      ) : (
        <FirstAlertLog />
      )}
    </div>
  );
}
