import React, { useState } from "react";
import { Search, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import TemplateForm from "@/components/Template/alertTemplate";
import { AlertCard } from "@/components/AlertCard";
import { Filter, FilterValues } from "@/components/Filter";
import { EnhancedPagination } from "@/components/ui/pagination";
import { useAlertTemplates } from "@/api/alertTemplates";
import { TemplateType, AlertTemplate } from "@/types/alertTemplate";
import { useEffect } from "react";
import EmptyTemplate from "./EmptyTemplate";

interface AlertCardProps {
  id: string;
  title: string;
  description: string;
  type: string;
  severity: "High Alert" | "Medium Alert" | "Low Alert";
  icon: string;
}

const mapSeverity = (
  severity: string,
): "High Alert" | "Medium Alert" | "Low Alert" => {
  switch (severity) {
    case "high":
      return "High Alert";
    case "medium":
      return "Medium Alert";
    case "low":
      return "Low Alert";
    default:
      return "Low Alert";
  }
};

const getHazardIcon = (hazardType: string): string => {
  switch (hazardType) {
    case "fire":
      return "/fire.svg";
    case "flood":
      return "/flood.svg";
    case "drought":
      return "/drought.svg";
    case "landslide":
      return "/landslide.svg";
    default:
      return "/icon.svg";
  }
};

const SMSAlertTemplate: React.FC = () => {
  const [showFilter, setShowFilter] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [filters, setFilters] = useState<FilterValues>({});

  const [displayTemplates, setDisplayTemplates] = useState<AlertTemplate[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  const {
    data: allTemplatesData,
    isLoading,
    isError,
    error,
    refetch,
  } = useAlertTemplates();

  const allTemplates = React.useMemo(() => {
    if (!allTemplatesData || !Array.isArray(allTemplatesData)) return [];
    return allTemplatesData.filter(
      (template: AlertTemplate) => template.template_type === TemplateType.SMS,
    );
  }, [allTemplatesData]);

  useEffect(() => {
    if (allTemplates.length > 0) {
      const filteredTemplates = allTemplates
        .filter((template) => {
          const matchesSearch = template.template_name
            .toLowerCase()
            .includes(searchTerm.toLowerCase());

          const matchesHazardType =
            !filters.hazardType || template.hazard_type === filters.hazardType;

          const matchesSeverity =
            !filters.severity || template.severity === filters.severity;

          let matchesDateRange = true;
          if (filters.startDate || filters.endDate) {
            const createdDate = new Date(template.created_at);
            if (filters.startDate && createdDate < filters.startDate) {
              matchesDateRange = false;
            }
            if (filters.endDate) {
              const endDatePlusOne = new Date(filters.endDate);
              endDatePlusOne.setDate(endDatePlusOne.getDate() + 1);
              if (createdDate > endDatePlusOne) {
                matchesDateRange = false;
              }
            }
          }

          return (
            matchesSearch &&
            matchesHazardType &&
            matchesSeverity &&
            matchesDateRange
          );
        })
        .sort(
          (a, b) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
        );

      setTotalItems(filteredTemplates.length);
      setTotalPages(Math.ceil(filteredTemplates.length / itemsPerPage));

      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = Math.min(
        startIndex + itemsPerPage,
        filteredTemplates.length,
      );
      const currentTemplates = filteredTemplates.slice(startIndex, endIndex);

      setDisplayTemplates(currentTemplates);
    }
  }, [currentPage, itemsPerPage, allTemplates, searchTerm, filters]);

  const hasSmsTemplates = allTemplates && allTemplates.length > 0;
  const hasFilteredTemplates = displayTemplates && displayTemplates.length > 0;

  const mappedTemplates: AlertCardProps[] = displayTemplates.map(
    (template) => ({
      id: template.id.toString(),
      title: template.template_name,
      description: template.alert_message,
      type:
        template.hazard_type.charAt(0).toUpperCase() +
        template.hazard_type.slice(1),
      severity: mapSeverity(template.severity),
      icon: getHazardIcon(template.hazard_type),
    }),
  );

  const handleApplyFilter = (newFilters: FilterValues) => {
    setFilters(newFilters);
    setCurrentPage(1);
  };

  const handleTemplateDeleted = () => {
    refetch();

    if (displayTemplates.length === 1 && currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handlePageChange = (page: number) => setCurrentPage(page);
  const handleItemsPerPageChange = (items: number) => {
    setItemsPerPage(items);
    setCurrentPage(1);
  };

  return (
    <div className="flex h-full w-full flex-col overflow-hidden bg-[#F5F5F5]">
      <div className="flex-none p-6 pb-3">
        <div className="flex items-center justify-between">
          <h1 className="poppins-medium text-xl text-slate-900">
            SMS Alert Template
          </h1>

          <div className="flex items-center gap-3">
            <div className="relative h-9 w-60">
              <Input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="h-full w-full rounded-lg border border-[#d9d9d9] bg-white pr-3 pl-10 text-sm"
              />
              <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
            </div>

            <TemplateForm
              onSuccess={handleTemplateDeleted}
              trigger={
                <Button className="poppins-regular h-10 rounded-lg bg-[linear-gradient(294.02deg,#02475C_0%,#1D8E89_100%)] px-6 py-2 text-base leading-[100%] whitespace-nowrap text-white transition hover:opacity-90">
                  Add New Alert
                </Button>
              }
            />

            <div className="relative">
              <Button
                variant="outline"
                size="icon"
                className={`h-10 w-10 rounded-lg ${
                  showFilter ? "bg-[#E9F3F0]" : "bg-white"
                } p-2`}
                onClick={() => setShowFilter(!showFilter)}
              >
                <img src="/filter.svg" alt="Filter" className="h-4 w-4" />
              </Button>
              {showFilter && (
                <Filter
                  onClose={() => setShowFilter(false)}
                  onApplyFilter={handleApplyFilter}
                  initialValues={filters}
                  showCommunicationFilter={false}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="flex min-h-0 flex-grow flex-col px-6 pb-3">
        <div className="flex min-h-0 flex-grow flex-col overflow-hidden rounded-lg bg-white">
          {isLoading ? (
            <div className="flex h-full items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-[#02475C]" />
              <span className="ml-2 text-[#02475C]">Loading templates...</span>
            </div>
          ) : isError ? (
            <div className="flex h-full items-center justify-center text-red-500">
              Error loading templates: {(error as Error).message}
            </div>
          ) : !hasSmsTemplates ? (
            <EmptyTemplate />
          ) : !hasFilteredTemplates ? (
            <div className="flex h-full items-center justify-center text-gray-500">
              No SMS templates found with the current filters
            </div>
          ) : (
            <div className="min-h-0 flex-grow overflow-y-auto">
              <div className="p-2">
                {mappedTemplates.map((template, index) => (
                  <AlertCard
                    key={template.id}
                    {...template}
                    isLast={index === mappedTemplates.length - 1}
                    templateData={displayTemplates[index]}
                    onDelete={handleTemplateDeleted}
                    onEdit={handleTemplateDeleted}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {!isLoading && !isError && hasFilteredTemplates && (
        <div className="mx-6 mb-3 flex-none">
          <EnhancedPagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}
    </div>
  );
};

export default SMSAlertTemplate;
