import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import AllAlerts from "./AllAlerts";
import EmailAlertTemplate from "./EmailAlertTemplate";
import SMSAlertTemplate from "./SMSAlertTemplate";
import IVRAlertTemplate from "./IVRAlertTemplate";

export default function AlertTemplate() {
  return (
    <div className="h-full bg-[#F5F5F5]">
      <Tabs defaultValue="all" className="h-full">
        <div className="border-b bg-white px-6 py-3">
          <TabsList className="grid w-[500px] grid-cols-4">
            <TabsTrigger value="all">All Templates</TabsTrigger>
            <TabsTrigger value="email">Email Templates</TabsTrigger>
            <TabsTrigger value="sms">SMS Templates</TabsTrigger>
            <TabsTrigger value="ivr">IVR Templates</TabsTrigger>
          </TabsList>
        </div>
        <TabsContent value="all" className="h-[calc(100%-64px)]">
          <AllAlerts />
        </TabsContent>
        <TabsContent value="email" className="h-[calc(100%-64px)]">
          <EmailAlertTemplate />
        </TabsContent>
        <TabsContent value="sms" className="h-[calc(100%-64px)]">
          <SMSAlertTemplate />
        </TabsContent>
        <TabsContent value="ivr" className="h-[calc(100%-64px)]">
          <IVRAlertTemplate />
        </TabsContent>
      </Tabs>
    </div>
  );
}
