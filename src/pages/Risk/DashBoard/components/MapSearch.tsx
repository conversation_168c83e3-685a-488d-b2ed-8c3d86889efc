import { useState, useEffect } from "react";
import {
  Command,
  CommandInput,
  CommandList,
  CommandGroup,
  CommandItem,
} from "@/components/ui/command";
import { useSearchAllStations, SearchResult } from "@/api/mapData";
import { useTranslation } from "react-i18next";
import { Loader2 } from "lucide-react";

interface Station {
  id: number | string;
  name: string;
  basin?: string;
  district?: string;
}

interface MapSearchProps {
  stations: Station[];
  onStationSelect: (
    stationId: number | null,
    stationType: "rainfall" | "waterLevel" | null,
  ) => void;
  placeholder?: string;
  className?: string;
  searchFilter?: "rainfall" | "waterLevel" | "all";
}

export default function MapSearch({
  stations,
  onStationSelect,
  placeholder = "Search stations or place",
  className = "w-64",
  searchFilter = "all",
}: MapSearchProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStation, setSelectedStation] = useState<SearchResult | null>(
    null,
  );
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const { t } = useTranslation();

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const { data: searchResults, isLoading: isSearching } = useSearchAllStations(
    debouncedSearchTerm,
    true,
  );

  const localSearchSuggestions = searchTerm
    ? stations.filter(
        (station) =>
          station.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (station.basin &&
            station.basin.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (station.district &&
            station.district.toLowerCase().includes(searchTerm.toLowerCase())),
      )
    : [];

  const getFilteredResults = () => {
    const rainfallResults = Array.isArray(searchResults?.rainfall)
      ? searchResults.rainfall.map((station) => ({
          ...station,
          id: station.id,
          name: station.station_name || station.name,
          type: "rainfall" as const,
        }))
      : [];

    const waterLevelResults = Array.isArray(searchResults?.waterLevel)
      ? searchResults.waterLevel.map((station) => ({
          ...station,
          id: station.id,
          name: station.station_name || station.name,
          type: "waterLevel" as const,
        }))
      : [];

    switch (searchFilter) {
      case "rainfall":
        return rainfallResults;
      case "waterLevel":
        return waterLevelResults;
      case "all":
      default: {
        const combinedResults = [...rainfallResults, ...waterLevelResults];
        return combinedResults;
      }
    }
  };

  const filteredResults = getFilteredResults();

  const mappedLocalSuggestions = localSearchSuggestions.map((station) => ({
    id:
      typeof station.id === "string"
        ? parseInt(station.id as string) || 0
        : (station.id as number),
    name: station.name,
    basin: station.basin || "",
    district: station.district,
    type: "local" as const,
  }));

  const allSuggestions = [...filteredResults, ...mappedLocalSuggestions];

  const handleStationSelect = (station: {
    id: number;
    name?: string;
    basin: string;
    district?: string;
    type: string;
    station_name?: string;
  }) => {
    setSelectedStation(station as SearchResult);
    setSearchTerm(station.name || station.station_name || "");

    if (station.type === "local") {
      onStationSelect(null, null);
    } else {
      const stationId =
        typeof station.id === "number" ? station.id : parseInt(station.id);

      let stationType: "rainfall" | "waterLevel";
      if (station.type === "rainfall") {
        stationType = "rainfall";
      } else if (station.type === "waterLevel") {
        stationType = "waterLevel";
      } else {
        stationType = "rainfall";
      }

      onStationSelect(stationId, stationType);
    }
  };

  const handleClearSearch = () => {
    setSearchTerm("");
    setDebouncedSearchTerm("");
    setSelectedStation(null);
    onStationSelect(null, null);
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);

    if (selectedStation && value !== selectedStation.name) {
      setSelectedStation(null);
      onStationSelect(null, null);
    }

    if (value === "") {
      setDebouncedSearchTerm("");
    }
  };

  return (
    <div className={`relative ${className}`}>
      <Command shouldFilter={false}>
        <CommandInput
          placeholder={placeholder}
          value={searchTerm}
          onValueChange={handleSearchChange}
        />
        {(searchTerm.length > 0 || debouncedSearchTerm.length >= 2) && (
          <CommandList className="absolute z-[100] mt-10 max-h-80 w-full overflow-y-auto rounded-md border border-[#e9e9e9] bg-white shadow-lg">
            {isSearching && debouncedSearchTerm.length >= 2 && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin text-[#1D8E89]" />
                <span className="ml-2 text-sm text-[#7b7b7b]">
                  {t("loading")}
                </span>
              </div>
            )}
            {!isSearching &&
              allSuggestions.length === 0 &&
              debouncedSearchTerm.length >= 2 && (
                <div className="flex items-center justify-center py-4 text-sm text-[#7b7b7b]">
                  <div className="text-center">
                    <div className="mb-2">
                      No stations found matching "{debouncedSearchTerm}"
                    </div>
                    <div className="text-xs">
                      {searchFilter === "waterLevel"
                        ? "Water level"
                        : searchFilter === "rainfall"
                          ? "Rainfall"
                          : "All"}{" "}
                      search
                    </div>
                  </div>
                </div>
              )}
            {!isSearching && allSuggestions.length > 0 && (
              <div className="border-b border-gray-200 bg-gray-50 px-3 py-2 text-xs font-medium text-gray-600">
                {allSuggestions.length} result
                {allSuggestions.length !== 1 ? "s" : ""} found
                {allSuggestions.length > 50 && (
                  <span className="ml-1 text-gray-500">(showing first 50)</span>
                )}
              </div>
            )}
            <CommandGroup>
              {allSuggestions.slice(0, 50).map((station) => (
                <CommandItem
                  key={`${station.type}-${station.id}`}
                  onSelect={() => handleStationSelect(station)}
                  className="cursor-pointer px-3 py-2 text-sm text-[#232323] hover:bg-[#f5f5f5]"
                >
                  <div className="flex flex-col">
                    <div className="font-medium">{station.name}</div>
                    <div className="flex items-center gap-2 text-xs text-[#7b7b7b]">
                      <span>{station.basin}</span>
                      {station.type !== "local" && (
                        <span
                          className={`rounded px-1.5 py-0.5 text-xs ${
                            station.type === "rainfall"
                              ? "bg-blue-100 text-blue-700"
                              : "bg-green-100 text-green-700"
                          }`}
                        >
                          {station.type === "rainfall"
                            ? t("rainfallWatch")
                            : t("riverWatch")}
                        </span>
                      )}
                    </div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
            {(searchTerm || debouncedSearchTerm || selectedStation) && (
              <div className="border-t border-[#e9e9e9] p-2">
                <button
                  onClick={handleClearSearch}
                  className="w-full px-2 py-1 text-center text-xs text-[#48c1b5] hover:underline"
                >
                  Clear search
                </button>
              </div>
            )}
          </CommandList>
        )}
      </Command>
    </div>
  );
}
