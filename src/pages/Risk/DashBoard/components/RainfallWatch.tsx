import { useState, useEffect, useCallback } from "react";
import { Search } from "lucide-react";
import { fetchRainfallData, StationData } from "@/api/mapData";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

interface RainfallWatchProps {
  rainfallData: {
    location: string;
    measurement: string;
    color: string;
  }[];
  isLoading?: boolean;
}

export default function RainfallWatch({
  rainfallData,
  isLoading = false,
}: RainfallWatchProps) {
  const [selectedPeriod, setSelectedPeriod] = useState("1 hr");
  const [searchTerm, setSearchTerm] = useState("");
  const [showSearch, setShowSearch] = useState(false);
  const [showAll] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [allRainfallData, setAllRainfallData] = useState<
    {
      location: string;
      measurement: string;
      color: string;
    }[]
  >([]);
  const { t } = useTranslation();

  const navigate = useNavigate();

  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading]);

  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading]);

  const timePeriods = [
    { key: "1hr", value: "1 hr" },
    { key: "3hr", value: "3 hr" },
    { key: "6hr", value: "6 hr" },
    { key: "12hr", value: "12 hr" },
    { key: "24hr", value: "24 hr" },
  ];

  const getMeasurementByPeriod = useCallback(
    (station: StationData) => {
      switch (selectedPeriod) {
        case "1 hr":
          return station.rainfall1h || 0;
        case "3 hr":
          return station.rainfall3h || 0;
        case "6 hr":
          return station.rainfall6h || 0;
        case "12 hr":
          return station.rainfall12h || 0;
        case "24 hr":
          return station.rainfall24h || 0;
        default:
          return station.rainfall24h || 0;
      }
    },
    [selectedPeriod],
  );

  const fetchMoreRainfallData = useCallback(async () => {
    if (showAll && allRainfallData.length > 0) {
      return;
    }

    setIsLoadingMore(true);
    try {
      const result = await fetchRainfallData();
      const formattedData = result.stations.map((station) => {
        const measurement = getMeasurementByPeriod(station);
        return {
          location: station.name,
          measurement: `${measurement} mm`,
          color: station.status?.toLowerCase().includes("danger")
            ? "#e13636"
            : station.status?.toLowerCase().includes("warning")
              ? "#ffae19"
              : "#48c1b5",
        };
      });
      setAllRainfallData(formattedData);
    } catch (error) {
      console.error("Error loading more rainfall data:", error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [showAll, allRainfallData.length, getMeasurementByPeriod]);

  useEffect(() => {
    if (allRainfallData.length > 0) {
      fetchMoreRainfallData();
    }
  }, [selectedPeriod, allRainfallData.length, fetchMoreRainfallData]);

  const filteredRainfallData = rainfallData.filter((item) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      item.location.toLowerCase().includes(searchLower) ||
      item.measurement.toLowerCase().includes(searchLower)
    );
  });

  const filteredAllRainfallData = allRainfallData.filter((item) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      item.location.toLowerCase().includes(searchLower) ||
      item.measurement.toLowerCase().includes(searchLower)
    );
  });

  const displayData = showAll
    ? isLoadingMore
      ? filteredRainfallData
      : filteredAllRainfallData
    : filteredRainfallData.slice(0, 7);

  return (
    <div className="flex h-full min-h-0 min-w-0 flex-col overflow-hidden rounded-lg border border-[#e9e9e9] bg-white shadow-sm">
      <div className="p-3">
        <div className="mb-2 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h1 className="font-['Lato'] text-lg font-normal text-[#232323]">
              {t("rainfallWatchTitle")}
            </h1>
            {searchTerm && (
              <span className="rounded bg-[#f5f5f5] px-2 py-1 text-xs text-[#7b7b7b]">
                {displayData.length}{" "}
                {displayData.length !== 1 ? t("results") : t("result")}
              </span>
            )}
          </div>
          <button
            onClick={() => {
              setShowSearch(!showSearch);
              if (showSearch) setSearchTerm("");
            }}
            className={`text-[#7b7b7b] hover:text-[#232323] ${searchTerm ? "text-[#48c1b5]" : ""}`}
          >
            <Search className="h-4 w-4" />
          </button>
        </div>
        {showSearch && (
          <div className="relative">
            <input
              type="text"
              placeholder={t("searchRainfall")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Escape") {
                  setSearchTerm("");
                  setShowSearch(false);
                }
              }}
              className="w-full rounded-md border border-[#e9e9e9] px-3 py-1 pl-8 text-sm focus:ring-1 focus:ring-[#48c1b5] focus:outline-none"
              autoFocus
            />
            <Search className="absolute top-1/2 left-2 h-4 w-4 -translate-y-1/2 transform text-[#7b7b7b]" />
          </div>
        )}
      </div>

      <div className="px-6 pb-2">
        <div className="flex gap-7">
          {timePeriods.map((period) => (
            <button
              key={period.value}
              onClick={() => setSelectedPeriod(period.value)}
              className={`border-b-2 pb-1 font-['Lato'] text-sm transition-colors ${
                selectedPeriod === period.value
                  ? "border-[#232323] font-medium text-[#232323]"
                  : "border-transparent text-[#7b7b7b] hover:text-[#232323]"
              }`}
            >
              {t(period.key)}
            </button>
          ))}
        </div>
      </div>

      <div className="mx-4 border-t border-[#e9e9e9]"></div>

      <div className="min-h-0 flex-1 overflow-y-auto px-3 py-1">
        {loading && (
          <div className="flex items-center justify-center py-4">
            <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-[#48c1b5]"></div>
          </div>
        )}

        {!loading && displayData.length === 0 && searchTerm && (
          <div className="flex items-center justify-center py-4 text-sm text-[#7b7b7b]">
            <div className="text-center">
              <div className="mb-2">
                No locations found matching "{searchTerm}"
              </div>
              <button
                onClick={() => setSearchTerm("")}
                className="text-xs text-[#48c1b5] hover:underline"
              >
                Clear search
              </button>
            </div>
          </div>
        )}

        {!loading && displayData.length === 0 && !searchTerm && (
          <div className="flex items-center justify-center py-4 text-sm text-[#7b7b7b]">
            {t("noRainfallData")}
          </div>
        )}

        {!loading && displayData.length > 0 && (
          <div className="space-y-1">
            {searchTerm && (
              <div className="mb-2 px-1 text-xs text-[#48c1b5]">
                {t("showing")} {displayData.length} {t("of")}{" "}
                {filteredRainfallData.length} {t("locations")}
              </div>
            )}
            {displayData.map((item, index) => (
              <div
                key={index}
                className="mb-[5px] flex items-center justify-between border-b border-[#e9e9e9] py-1 last:border-b-0"
              >
                <span className="mb-[5px] truncate pr-2 font-['Lato'] text-xs text-[#000000]">
                  {item.location}
                </span>
                <span className="ml-2 font-['Lato'] text-xs font-normal">
                  {item.measurement}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-auto flex justify-center px-3 pb-2">
        <button
          onClick={() => navigate("/risk/real-time-monitoring?tab=rainfall")}
          className="flex items-center font-['Lato'] text-sm font-medium text-[#02475c]"
        >
          {t("showAll")}
        </button>
      </div>
    </div>
  );
}
