import { useState } from "react";
import { Search } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

interface RiverWatchProps {
  riverData: {
    name: string;
    level: string;
    status: string;
  }[];
  isLoading?: boolean;
}

export default function RiverWatch({
  riverData,
  isLoading = false,
}: RiverWatchProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [showSearch, setShowSearch] = useState(false);
  const { t } = useTranslation();

  const navigate = useNavigate();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "normal":
        return "#208661";
      case "warning":
        return "#208661";
      case "danger":
        return "#E13636";
      default:
        return "#208661";
    }
  };

  const filteredRiverData = riverData.filter((river) =>
    river.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const displayData = filteredRiverData.slice(0, 7);

  return (
    <div className="flex h-full min-h-0 min-w-0 flex-col overflow-hidden rounded-lg border border-[#e9e9e9] bg-white shadow-sm">
      <div className="p-3">
        <div className="mb-2 flex items-center justify-between">
          <h1 className="font-['Lato'] text-lg font-normal text-[#232323]">
            {t("riverWatchTitle")}
          </h1>
          <button
            onClick={() => setShowSearch(!showSearch)}
            className="text-[#7b7b7b] hover:text-[#232323]"
          >
            <Search className="h-4 w-4" />
          </button>
        </div>
        {showSearch && (
          <div className="relative">
            <input
              type="text"
              placeholder={t("searchRivers")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full rounded-md border border-[#e9e9e9] px-3 py-1 pl-8 text-sm focus:ring-1 focus:ring-[#48c1b5] focus:outline-none"
            />
            <Search className="absolute top-1/2 left-2 h-4 w-4 -translate-y-1/2 transform text-[#7b7b7b]" />
          </div>
        )}
      </div>
      <div className="mt-[-2px] mb-2 border-1 border-t border-[#D9D9D9]"></div>

      <div className="min-h-0 flex-1 overflow-y-auto px-3 pb-1">
        {isLoading && (
          <div className="flex items-center justify-center py-4">
            <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-[#48c1b5]"></div>
          </div>
        )}

        {!isLoading && displayData.length === 0 && searchTerm && (
          <div className="flex items-center justify-center py-4 text-sm text-[#7b7b7b]">
            {t("noRiversFound")} "{searchTerm}"
          </div>
        )}

        {!isLoading && displayData.length > 0 && (
          <div className="space-y-1">
            {displayData.map((river, index) => (
              <div
                key={index}
                className="mb-[5px] flex items-center justify-between border-b border-[#e9e9e9] py-1 last:border-b-0"
              >
                <div className="flex-1">
                  <p className="truncate pr-2 font-['Lato'] text-[13px] leading-tight font-normal text-[#000000]">
                    {river.name}
                  </p>
                </div>
                <div className="ml-4">
                  <span
                    className="font-['Lato'] text-xs font-normal"
                    style={{ color: getStatusColor(river.status) }}
                  >
                    {river.level}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-auto flex justify-center px-3 pb-2">
        <button
          onClick={() => navigate("/risk/real-time-monitoring")}
          className="flex items-center font-['Lato'] text-sm font-medium text-[#02475c]"
        >
          {t("showAll")}
        </button>
      </div>
    </div>
  );
}
