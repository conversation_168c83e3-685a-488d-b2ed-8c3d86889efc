import React from "react";
import { Card } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { WeatherData } from "@/api/mapData";
import { useTranslation } from "react-i18next";

const getWeatherIcon = (condition: string): string => {
  const conditionLower = condition.toLowerCase();

  if (conditionLower.includes("cloud") && conditionLower.includes("part")) {
    return "/weather-partly-cloudy.svg";
  } else if (conditionLower.includes("cloud")) {
    return "/weather-cloudy.svg";
  } else if (
    conditionLower.includes("rain") ||
    conditionLower.includes("drizzle")
  ) {
    return "/weather-rain.svg";
  } else if (
    conditionLower.includes("thunder") ||
    conditionLower.includes("storm")
  ) {
    return "/weather-rain.svg";
  } else if (conditionLower.includes("snow")) {
    return "/weather-cloudy.svg";
  } else if (
    conditionLower.includes("mist") ||
    conditionLower.includes("fog")
  ) {
    return "/weather-cloudy.svg";
  } else {
    return "/weather-sunny.svg";
  }
};

interface WeatherCardProps {
  weatherData: WeatherData | null;
  isLoading: boolean;
}

const WeatherCard: React.FC<WeatherCardProps> = ({
  weatherData,
  isLoading,
}) => {
  const { t } = useTranslation();

  return (
    <Card className="flex h-[70px] w-full flex-col items-center justify-center gap-2 rounded-[12px] p-2">
      {isLoading ? (
        <div className="flex items-center justify-center">
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          <span className="poppins-regular text-[12px] text-[#475467]">
            {t("weatherLoading")}
          </span>
        </div>
      ) : (
        <div className="flex w-full items-center justify-center gap-3">
          <div className="flex items-center justify-center">
            <img
              src={
                weatherData?.icon
                  ? getWeatherIcon(weatherData.condition)
                  : "/weather-partly-cloudy.svg"
              }
              alt={weatherData?.condition || "Weather"}
              className="h-14 w-14"
            />
          </div>
          <div className="flex flex-col items-start justify-center">
            <div className="flex items-baseline gap-1">
              <span className="poppins-semibold text-[28px] text-[#46556a]">
                {weatherData?.temperature || t("noWeatherData")}°
              </span>
              <span className="text-[20px] font-medium text-[#46556a]">c</span>
            </div>
            <div className="text-[16px] text-[#667085]">
              {weatherData?.condition || t("partialCloudy")}
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default WeatherCard;
