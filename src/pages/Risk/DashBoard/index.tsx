"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useState, lazy, Suspense } from "react";
import { Loader2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { fetchWeatherData } from "@/api/mapData";
import { useDashboardData } from "@/hooks/useDashboardData";
import { useSentry } from "@/hooks/useSentry";
import { useStatusCount } from "@/hooks/useStatusCount";
import WeatherCard from "./components/WeatherCard";
import RiverWatch from "./components/RiverWatch";
import RainfallWatch from "./components/RainfallWatch";
import MapSearch from "./components/MapSearch";
import { useStationFocus } from "@/hooks/useStationFocus";

const DynamicMapView = lazy(
  () => import("@/components/MapLibre/OptimizedMapView"),
);

const weatherKeys = {
  all: ["weather"] as const,
  current: () => [...weatherKeys.all, "current"] as const,
} as const;

export default function Dashboard() {
  const [selectedStation, setSelectedStation] = useState<string | null>(null);
  const [focusedStationId, setFocusedStationId] = useState<number | null>(null);
  const [focusedStationType, setFocusedStationType] = useState<
    "rainfall" | "waterLevel" | null
  >(null);
  const [activeTab, setActiveTab] = useState<
    "water-level" | "rainfall" | "overall"
  >("overall");

  const { t } = useTranslation();
  const { reportError } = useSentry();
  const { focusOnStation } = useStationFocus();

  const {
    alerts: alertLogs,
    rainfallCounts,
    waterLevelCounts,
    overallCounts,
    hasAnyError,
    refreshAllData,
  } = useDashboardData();

  const statusCountHook = useStatusCount();

  const getLastUpdatedDisplay = () => {
    const { lastUpdatedRainfall, lastUpdatedWaterLevel } = statusCountHook;

    if (!lastUpdatedRainfall || !lastUpdatedWaterLevel) {
      return {
        showSingle: true,
        singleTimestamp: new Date().toLocaleString(),
        rainfallTimestamp: null,
        waterLevelTimestamp: null,
      };
    }

    const rainfallTime = new Date(lastUpdatedRainfall);
    const waterLevelTime = new Date(lastUpdatedWaterLevel);

    const timeDifference = Math.abs(
      rainfallTime.getTime() - waterLevelTime.getTime(),
    );
    const oneMinuteInMs = 60 * 1000;

    if (timeDifference <= oneMinuteInMs) {
      const latestTime =
        rainfallTime > waterLevelTime ? rainfallTime : waterLevelTime;
      return {
        showSingle: true,
        singleTimestamp: latestTime.toLocaleString(),
        rainfallTimestamp: null,
        waterLevelTimestamp: null,
      };
    } else {
      return {
        showSingle: false,
        singleTimestamp: null,
        rainfallTimestamp: rainfallTime.toLocaleString(),
        waterLevelTimestamp: waterLevelTime.toLocaleString(),
      };
    }
  };

  const lastUpdatedInfo = getLastUpdatedDisplay();
  const { data: weatherData, isLoading: isLoadingWeather } = useQuery({
    queryKey: weatherKeys.current(),
    queryFn: fetchWeatherData,
    staleTime: 5 * 60 * 1000,
    refetchInterval: 10 * 60 * 1000,
    refetchOnWindowFocus: true,
  });

  const getCurrentStationCounts = () => {
    switch (activeTab) {
      case "rainfall":
        return rainfallCounts;
      case "water-level":
        return waterLevelCounts;
      case "overall":
      default:
        return overallCounts;
    }
  };

  const currentStationCounts = getCurrentStationCounts();

  const handleStationSelect = (
    stationId: number | null,
    stationType: "rainfall" | "waterLevel" | null,
  ) => {
    try {
      if (stationId && stationType) {
        setFocusedStationId(stationId);
        setFocusedStationType(stationType);
        setSelectedStation(`${stationType}-${stationId}`);

        focusOnStation();
      } else {
        setSelectedStation(null);
        setFocusedStationId(null);
        setFocusedStationType(null);
      }
    } catch (error) {
      reportError(error as Error, {
        action: "handleStationSelect",
        stationId,
        stationType,
        component: "Dashboard",
      });
    }
  };

  return (
    <div className="flex h-full min-h-0 w-full flex-col overflow-hidden bg-[#F5F5F5]">
      <div className="flex-none p-6 pb-4">
        <div className="flex gap-4">
          <div className="flex flex-1 gap-4">
            <Card className="flex h-[70px] flex-1 flex-row gap-3 rounded-[12px] p-2">
              <img src="/Below.svg" className="h-12 w-12" />
              <div className="flex w-full flex-col">
                <CardHeader className="p-0 pb-1">
                  <CardTitle className="poppins-regular text-[12px] text-[#475467]">
                    {t("belowWarning")}
                  </CardTitle>
                </CardHeader>
                <CardContent className="poppins-semibold p-0 text-[17px] text-[#208661]">
                  {currentStationCounts.belowWarning}
                </CardContent>
              </div>
            </Card>

            <Card className="flex h-[70px] flex-1 flex-row gap-3 rounded-[12px] p-2">
              <img src="/Warn.svg" className="h-12 w-12" />
              <div className="flex w-full flex-col">
                <CardHeader className="p-0 pb-1">
                  <CardTitle className="poppins-regular text-[12px] text-[#475467]">
                    {t("warning")}
                  </CardTitle>
                </CardHeader>
                <CardContent className="poppins-semibold p-0 text-[17px] text-[#FFAE19]">
                  {currentStationCounts.warning}
                </CardContent>
              </div>
            </Card>

            <Card className="flex h-[70px] flex-1 flex-row gap-3 rounded-[12px] p-2">
              <img src="/Up.svg" className="h-12 w-12" />
              <div className="flex w-full flex-col">
                <CardHeader className="p-0 pb-1">
                  <CardTitle className="poppins-regular text-[12px] text-[#475467]">
                    {t("danger")}
                  </CardTitle>
                </CardHeader>
                <CardContent className="poppins-semibold p-0 text-[17px] text-[#E13636]">
                  {currentStationCounts.danger}
                </CardContent>
              </div>
            </Card>

            <Card className="flex h-[70px] flex-1 flex-row gap-3 rounded-[12px] p-2">
              <img src="/alertsend.svg" className="h-12 w-12" />
              <div className="flex w-full flex-col">
                <CardHeader className="p-0 pb-1">
                  <CardTitle className="poppins-regular text-[12px] text-[#475467]">
                    {t("alertSent")}
                  </CardTitle>
                </CardHeader>
                <CardContent className="poppins-semibold p-0 text-[17px]">
                  {alertLogs?.length || 0}
                </CardContent>
              </div>
            </Card>
          </div>

          <div className="relative w-80">
            <WeatherCard
              weatherData={weatherData || null}
              isLoading={isLoadingWeather}
            />
          </div>
        </div>
      </div>

      <div className="flex min-h-0 flex-1 gap-3 px-6">
        <div className="relative flex-grow overflow-hidden">
          <div className="bg-opacity-95 absolute top-4 right-4 z-20 flex items-start gap-2 rounded-lg p-3">
            <Tabs
              value={activeTab}
              onValueChange={(value) => {
                try {
                  const newTab = value as
                    | "water-level"
                    | "rainfall"
                    | "overall";
                  setActiveTab(newTab);
                } catch (error) {
                  reportError(error as Error, {
                    action: "tab_change",
                    component: "Dashboard",
                    attemptedTab: value,
                    selectedStation,
                  });
                }
              }}
              className="inline-flex items-start rounded-none bg-[#E9F3F0] p-1"
            >
              <TabsList className="flex gap-1 bg-transparent p-0 px-0">
                <TabsTrigger
                  value="overall"
                  className="flex-[0_0_auto] px-3 py-0 text-xs font-normal transition-colors data-[state=active]:bg-[#02475C] data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-[#02475C]"
                >
                  {t("overall")}
                </TabsTrigger>

                <TabsTrigger
                  value="water-level"
                  className="flex-[0_0_auto] px-3 py-0 text-xs font-normal transition-colors data-[state=active]:bg-[#02475C] data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-[#02475C]"
                >
                  {t("riverWatch")}
                </TabsTrigger>
                <TabsTrigger
                  value="rainfall"
                  className="flex-[0_0_auto] px-3 py-0 text-xs font-normal transition-colors data-[state=active]:bg-[#02475C] data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-[#02475C]"
                >
                  {t("rainfallWatch")}
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <MapSearch
              stations={[]}
              onStationSelect={handleStationSelect}
              placeholder={
                activeTab === "rainfall"
                  ? t("searchRainfallStations") || "Search rainfall stations"
                  : activeTab === "water-level"
                    ? t("searchWaterLevelStations") ||
                      "Search water level stations"
                    : t("searchStations") || "Search stations"
              }
              className="w-64"
              searchFilter={
                activeTab === "rainfall"
                  ? "rainfall"
                  : activeTab === "water-level"
                    ? "waterLevel"
                    : "all"
              }
            />
          </div>

          <div className="h-full w-full overflow-hidden rounded-lg bg-white">
            {hasAnyError ? (
              <div className="flex h-full items-center justify-center">
                <div className="flex flex-col items-center">
                  <div className="mb-4 rounded-full bg-red-100 p-3 text-red-600">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <p className="text-center text-gray-600">
                    {t("failedToLoad")}
                  </p>
                  <button
                    onClick={() => refreshAllData()}
                    className="mt-4 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                  >
                    {t("retry")}
                  </button>
                </div>
              </div>
            ) : (
              <Suspense
                fallback={
                  <div className="flex h-full items-center justify-center">
                    <Loader2 className="h-8 w-8 animate-spin text-[#1D8E89]" />
                    <span className="ml-2 text-lg text-gray-600">
                      {t("loadingMap")}
                    </span>
                  </div>
                }
              >
                <DynamicMapView
                  dataType={
                    activeTab === "water-level"
                      ? "waterLevel"
                      : activeTab === "rainfall"
                        ? "rainfall"
                        : "both"
                  }
                  focusedStation={
                    focusedStationId && focusedStationType
                      ? { id: focusedStationId, type: focusedStationType }
                      : null
                  }
                  onClearFocus={() => {
                    setFocusedStationId(null);
                    setFocusedStationType(null);
                    setSelectedStation(null);
                  }}
                />
              </Suspense>
            )}
          </div>
        </div>

        <div
          className="flex w-80 min-w-0 flex-none flex-col gap-3"
          style={{ height: "calc(101vh - 140px)" }}
        >
          <div className="min-h-0 flex-1">
            <RiverWatch
              riverData={[
                {
                  name: "Ninda khola at East West High",
                  level: "2.968 m",
                  status: "normal",
                },
                {
                  name: "Narayani at Devghat",
                  level: "2.968 m",
                  status: "normal",
                },
                {
                  name: "Phalakhu Khola at Betrawati",
                  level: "2.968 m",
                  status: "normal",
                },
                {
                  name: "Nalgad at Raulakhet",
                  level: "2.968 m",
                  status: "warning",
                },
                {
                  name: "Myagdi at Mangalghat",
                  level: "2.968 m",
                  status: "warning",
                },
                {
                  name: "Nalgad at Raulakhet",
                  level: "2.968 m",
                  status: "danger",
                },
                {
                  name: "Mohana River at Malakheti",
                  level: "2.968 m",
                  status: "danger",
                },
              ]}
              isLoading={false}
            />
          </div>

          <div className="min-h-0 flex-1">
            <RainfallWatch
              rainfallData={[
                {
                  location: "Ninda khola at East West High",
                  measurement: "2.968 mm",
                  color: "#48c1b5",
                },
                {
                  location: "Narayani at Devghat",
                  measurement: "2.968 mm",
                  color: "#48c1b5",
                },
                {
                  location: "Phalakhu Khola at Betrawati",
                  measurement: "2.968 mm",
                  color: "#48c1b5",
                },
                {
                  location: "Nalgad at Raulakhet",
                  measurement: "2.968 mm",
                  color: "#ffae19",
                },
                {
                  location: "Myagdi at Mangalghat",
                  measurement: "2.968 mm",
                  color: "#ffae19",
                },
                {
                  location: "Nalgad at Raulakhet",
                  measurement: "2.968 mm",
                  color: "#e13636",
                },
                {
                  location: "Mohana River at Malakheti",
                  measurement: "2.968 mm",
                  color: "#e13636",
                },
              ]}
              isLoading={false}
            />
          </div>
        </div>
      </div>

      <div className="mt-2 mr-90 flex justify-end pb-[15px]">
        <div className="flex items-center justify-end text-[15px]">
          <div className="flex items-center">
            <span className="Lato-regular text-[14px] text-[#46556a]">
              {t("dataSource")}:
            </span>
            <a
              href="https://www.dhm.gov.np/"
              target="_blank"
              rel="noopener noreferrer"
              className="ml-3 cursor-pointer text-[14px] font-semibold text-[#1D8E89] transition-colors hover:text-[#156B66]"
            >
              DHM
            </a>
          </div>
          <div className="mx-6 h-6 w-px bg-[#d7d8db]"></div>
          {lastUpdatedInfo.showSingle ? (
            <div className="flex items-center">
              <span className="Lato-regular text-[14px] text-[#46556a]">
                {t("lastUpdated")}:
              </span>
              <span className="ml-3 text-[14px] text-[#46556a]">
                {lastUpdatedInfo.singleTimestamp}
              </span>
            </div>
          ) : (
            <div className="flex flex-col gap-1">
              <div className="flex items-center">
                <span className="Lato-regular text-[12px] text-[#46556a]">
                  {t("rainfallUpdated")}:
                </span>
                <span className="ml-2 text-[12px] text-[#46556a]">
                  {lastUpdatedInfo.rainfallTimestamp}
                </span>
              </div>
              <div className="flex items-center">
                <span className="Lato-regular text-[12px] text-[#46556a]">
                  {t("riverUpdated")}:
                </span>
                <span className="ml-2 text-[12px] text-[#46556a]">
                  {lastUpdatedInfo.waterLevelTimestamp}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
