import React, { useState, useEffect } from "react";
import { Search, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import GroupDialog from "@/components/Template/group";
import { EnhancedPagination } from "@/components/ui/pagination";
import { useQuery } from "@tanstack/react-query";
import { fetchAllGroupsDirect, useDeleteGroup } from "@/api/groups";
import { CategoryEnum, Group as GroupType, SectorEnum } from "@/types/group";
import { toast } from "sonner";
import FirstGroup from "./EmptyGroup";

const groupsKeys = {
  all: ["groups"] as const,
  list: (filters: { category?: CategoryEnum }) =>
    [...groupsKeys.all, "list", filters] as const,
} as const;

const EditIcon = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.05 20.95H12.0063L19.0375 13.9187L18.0813 12.9625L11.05 19.9937V20.95ZM10.375 22.3C10.1875 22.3 10.025 22.2375 9.8875 22.1125C9.7625 21.975 9.7 21.8125 9.7 21.625V19.9937C9.7 19.8187 9.73125 19.65 9.79375 19.4875C9.85625 19.325 9.95625 19.175 10.0938 19.0375L19.0375 10.0937C19.175 9.95625 19.325 9.85625 19.4875 9.79375C19.65 9.73125 19.8188 9.7 19.9938 9.7C20.1688 9.7 20.3375 9.73125 20.5 9.79375C20.6625 9.85625 20.8125 9.95625 20.95 10.0937L21.9063 11.05C22.0438 11.1875 22.1438 11.3375 22.2063 11.5C22.2688 11.6625 22.3 11.8312 22.3 12.0062C22.3 12.1812 22.2688 12.35 22.2063 12.5125C22.1438 12.675 22.0438 12.825 21.9063 12.9625L12.9625 21.9062C12.825 22.0437 12.675 22.1437 12.5125 22.2062C12.35 22.2687 12.1813 22.3 12.0063 22.3H10.375ZM20.95 12.0062L19.9938 11.05L20.95 12.0062ZM18.55 13.45L18.0813 12.9625L19.0375 13.9187L18.55 13.45Z"
      fill="#667085"
    />
  </svg>
);

const DeleteIcon = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.85 22.3C12.475 22.3 12.1563 22.1687 11.8938 21.9062C11.6313 21.6437 11.5 21.325 11.5 20.95V11.95H11.275C11.0875 11.95 10.925 11.8875 10.7875 11.7625C10.6625 11.625 10.6 11.4625 10.6 11.275C10.6 11.0875 10.6625 10.9312 10.7875 10.8062C10.925 10.6687 11.0875 10.6 11.275 10.6H14.2V10.375C14.2 10.1875 14.2625 10.0312 14.3875 9.90625C14.525 9.76875 14.6875 9.7 14.875 9.7H17.125C17.3125 9.7 17.4688 9.76875 17.5938 9.90625C17.7313 10.0312 17.8 10.1875 17.8 10.375V10.6H20.725C20.9125 10.6 21.0688 10.6687 21.1938 10.8062C21.3313 10.9312 21.4 11.0875 21.4 11.275C21.4 11.4625 21.3313 11.625 21.1938 11.7625C21.0688 11.8875 20.9125 11.95 20.725 11.95H20.5V20.95C20.5 21.325 20.3688 21.6437 20.1063 21.9062C19.8438 22.1687 19.525 22.3 19.15 22.3H12.85ZM19.15 11.95H12.85V20.95H19.15V11.95ZM14.875 19.6C15.0625 19.6 15.2188 19.5375 15.3438 19.4125C15.4813 19.275 15.55 19.1125 15.55 18.925V13.975C15.55 13.7875 15.4813 13.6312 15.3438 13.5062C15.2188 13.3687 15.0625 13.3 14.875 13.3C14.6875 13.3 14.525 13.3687 14.3875 13.5062C14.2625 13.6312 14.2 13.7875 14.2 13.975V18.925C14.2 19.1125 14.2625 19.275 14.3875 19.4125C14.525 19.5375 14.6875 19.6 14.875 19.6ZM17.125 19.6C17.3125 19.6 17.4688 19.5375 17.5938 19.4125C17.7313 19.275 17.8 19.1125 17.8 18.925V13.975C17.8 13.7875 17.7313 13.6312 17.5938 13.5062C17.4688 13.3687 17.3125 13.3 17.125 13.3C16.9375 13.3 16.775 13.3687 16.6375 13.5062C16.5125 13.6312 16.45 13.7875 16.45 13.975V18.925C16.45 19.1125 16.5125 19.275 16.6375 19.4125C16.775 19.5375 16.9375 19.6 17.125 19.6ZM12.85 11.95V20.95V11.95Z"
      fill="#667085"
    />
  </svg>
);

const fallbackGroupData = [
  {
    id: 1,
    group_name: "Ward 1 - Health",
    location: "Sudurpashim, Dadeldhura, Prashuram, Ward 1",
    sector: SectorEnum.EDUCATION,
    category: CategoryEnum.GROUP,
  },
  {
    id: 2,
    group_name: "Group 2",
    location: "Kathmandu",
    sector: SectorEnum.HEALTH,
    category: CategoryEnum.GROUP,
  },
  {
    id: 3,
    group_name: "Mahakali",
    location: "Kanchanpur",
    sector: SectorEnum.GOVERNMENT,
    category: CategoryEnum.GROUP,
  },
  {
    id: 4,
    group_name: "Narayani",
    location: "Chitwan",
    sector: SectorEnum.EDUCATION,
    category: CategoryEnum.GROUP,
  },
];

const Group: React.FC = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [useFallbackData, setUseFallbackData] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [groupToDelete, setGroupToDelete] = useState<GroupType | null>(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [groupToEdit, setGroupToEdit] = useState<GroupType | null>(null);

  const {
    data: allGroups = [],
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: groupsKeys.list({ category: CategoryEnum.GROUP }),
    queryFn: () => fetchAllGroupsDirect({ category: CategoryEnum.GROUP }),

    enabled: !useFallbackData,
  });

  const deleteGroup = useDeleteGroup();

  const [displayGroups, setDisplayGroups] = useState<GroupType[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    if (isError && !useFallbackData) {
      toast.error("API Error: Using fallback data until the API is fixed");
      setUseFallbackData(true);
    }
  }, [isError, error, useFallbackData]);

  useEffect(() => {
    if (allGroups.length > 0 && !isError) {
      setTotalItems(allGroups.length);
      setTotalPages(Math.ceil(allGroups.length / itemsPerPage));
    }
  }, [allGroups, itemsPerPage, isError]);

  const handleRefetch = async () => {
    setUseFallbackData(false);
    await refetch();
  };

  const handleEditClick = (group: GroupType) => {
    setGroupToEdit(group);
    setShowEditDialog(true);
  };

  const handleEditSuccess = () => {
    setShowEditDialog(false);
    setGroupToEdit(null);

    if (!useFallbackData) {
      refetch();
    }
  };

  const handleDeleteClick = (group: GroupType) => {
    setGroupToDelete(group);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!groupToDelete) return;

    try {
      await deleteGroup.mutateAsync(groupToDelete.id);
      toast.success("Group deleted successfully");
      setShowDeleteDialog(false);
      setGroupToDelete(null);

      if (displayGroups.length === 1 && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      }
    } catch {
      toast.error("Failed to delete group");
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
    setGroupToDelete(null);
  };

  useEffect(() => {
    if (useFallbackData) {
      const filteredFallbackData = fallbackGroupData.filter(
        (group) =>
          group.group_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          group.location.toLowerCase().includes(searchTerm.toLowerCase()),
      );

      setTotalItems(filteredFallbackData.length);
      setTotalPages(Math.ceil(filteredFallbackData.length / itemsPerPage));

      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = Math.min(
        startIndex + itemsPerPage,
        filteredFallbackData.length,
      );
      const currentPageData = filteredFallbackData.slice(startIndex, endIndex);

      setDisplayGroups(currentPageData);
    } else if (allGroups.length > 0) {
      const filteredGroups = allGroups.filter(
        (group) =>
          group.group_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          group.location.toLowerCase().includes(searchTerm.toLowerCase()),
      );

      setTotalItems(filteredGroups.length);
      setTotalPages(Math.ceil(filteredGroups.length / itemsPerPage));

      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = Math.min(
        startIndex + itemsPerPage,
        filteredGroups.length,
      );
      const currentPageData = filteredGroups.slice(startIndex, endIndex);

      setDisplayGroups(currentPageData);
    }
  }, [currentPage, itemsPerPage, allGroups, searchTerm, useFallbackData]);

  useEffect(() => {
    if (!openDialog) {
      refetch();
    }
  }, [openDialog, refetch]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (items: number) => {
    setItemsPerPage(items);
    setCurrentPage(1);

    if (totalItems > 0) {
      setTotalPages(Math.ceil(totalItems / items));
    }
  };

  const formatSector = (sector: string | null | undefined) => {
    if (!sector) return "-";

    const sectorMap: Record<string, string> = {
      government: "Local Government",
      education: "Education",
      health: "Health",
      agriculture: "Agriculture",
      water_supply: "Water Supply",
    };

    return sectorMap[sector.toLowerCase()] || sector;
  };

  const hasGroups = !isLoading && !isError && displayGroups.length > 0;
  const noMatchingGroups =
    !isLoading && !isError && searchTerm && displayGroups.length === 0;

  return (
    <div className="flex h-full w-full flex-col overflow-hidden bg-[#F5F5F5]">
      <div className="flex-none p-6 pb-3">
        <div className="flex items-center justify-between">
          <h1 className="font-poppins text-xl leading-normal font-medium text-slate-900">
            Groups
          </h1>

          <div className="flex items-center gap-3">
            <div className="relative h-9 w-60">
              <Input
                type="text"
                placeholder="Search"
                className="font-poppins h-full w-full rounded-lg border border-[#d9d9d9] bg-white pr-3 pl-10 text-sm"
                value={searchTerm}
                onChange={handleSearch}
              />
              <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
            </div>

            <GroupDialog
              trigger={
                <Button className="poppins-regular h-10 cursor-pointer rounded-lg bg-gradient-to-l from-[#02475C] to-[#1D8E89] px-6 py-2 text-base leading-[100%] whitespace-nowrap text-white transition hover:opacity-90">
                  Add Group
                </Button>
              }
              open={openDialog}
              setOpen={setOpenDialog}
            />
          </div>
        </div>
      </div>

      {useFallbackData && (
        <div className="flex-none px-6 pb-3">
          <div className="rounded-md border border-amber-200 bg-amber-50 p-3 text-amber-800">
            <p className="text-sm">
              Using demo data. The API endpoint for listing groups is not
              available now.
            </p>
          </div>
        </div>
      )}

      <div className="flex min-h-0 flex-grow flex-col px-6 pb-3">
        <div className="flex min-h-0 flex-grow flex-col overflow-hidden rounded-lg bg-white">
          {isLoading && !useFallbackData ? (
            <div className="flex h-full items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-[#1D8E89]" />
              <span className="ml-2 text-lg text-gray-600">
                Loading groups...
              </span>
            </div>
          ) : !useFallbackData && isError ? (
            <div className="flex h-full flex-col items-center justify-center text-center">
              <p className="mb-4 text-lg text-red-600">Failed to load groups</p>
              <Button
                variant="outline"
                onClick={handleRefetch}
                className="border-[#1D8E89] text-[#1D8E89]"
              >
                Try Again
              </Button>
              <Button
                variant="outline"
                onClick={() => setUseFallbackData(true)}
                className="mt-2 border-amber-600 text-amber-600"
              >
                Use Demo Data
              </Button>
            </div>
          ) : (allGroups.length === 0 || displayGroups.length === 0) &&
            !searchTerm ? (
            <FirstGroup />
          ) : noMatchingGroups ? (
            <div className="flex h-full flex-col items-center justify-center text-center">
              <p className="mb-2 text-lg text-gray-600">No groups found</p>
              <p className="text-sm text-gray-500">
                Try a different search term
              </p>
            </div>
          ) : (
            <div className="flex h-full flex-col">
              <div className="flex-none border-b bg-white">
                <Table className="w-full table-fixed">
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16 text-center">S.N.</TableHead>
                      <TableHead className="w-1/3">Group Name</TableHead>
                      <TableHead className="w-1/4">Location</TableHead>
                      <TableHead className="w-1/6">Sector</TableHead>
                      <TableHead className="w-32 text-center">Action</TableHead>
                    </TableRow>
                  </TableHeader>
                </Table>
              </div>

              <div className="min-h-0 flex-grow overflow-y-auto">
                <Table className="w-full table-fixed">
                  <TableBody>
                    {displayGroups.map((item: GroupType, index: number) => (
                      <TableRow key={item.id}>
                        <TableCell className="w-16 text-center">
                          {(currentPage - 1) * itemsPerPage + index + 1}
                        </TableCell>
                        <TableCell
                          className="w-1/3 truncate"
                          title={item.group_name || "-"}
                        >
                          {item.group_name || "-"}
                        </TableCell>
                        <TableCell
                          className="w-1/4 truncate"
                          title={item.location}
                        >
                          {item.location}
                        </TableCell>
                        <TableCell
                          className="w-1/6 truncate"
                          title={formatSector(item.sector)}
                        >
                          {formatSector(item.sector)}
                        </TableCell>
                        <TableCell className="w-32">
                          <div className="flex items-center justify-center gap-2">
                            <div
                              className="cursor-pointer p-1 hover:opacity-70"
                              onClick={() => handleEditClick(item)}
                            >
                              <EditIcon />
                            </div>
                            <div
                              className="cursor-pointer p-1 hover:opacity-70"
                              onClick={() => handleDeleteClick(item)}
                            >
                              <DeleteIcon />
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </div>
      </div>

      {hasGroups && (
        <div className="mx-6 mb-3 flex-none">
          <EnhancedPagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}

      <GroupDialog
        trigger={null}
        open={showEditDialog}
        setOpen={setShowEditDialog}
        editData={groupToEdit}
        onSuccess={handleEditSuccess}
      />

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="rounded-lg p-6 sm:max-w-[425px]">
          <DialogHeader className="mb-6">
            <DialogTitle className="Lato-semibold text-[16px] font-semibold text-[#262626]">
              Delete Group
            </DialogTitle>
            <DialogDescription className="Lato-regular mt-4 text-[14px] text-[#667085]">
              Are you sure want to delete "{groupToDelete?.group_name}" Group?
              Once deleted the action is undone
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-end gap-[8px]">
            <Button
              type="button"
              onClick={handleDeleteConfirm}
              className="flex h-[32px] w-[90px] items-center justify-center rounded-[8px] border border-[#02475C] bg-white px-[24px] py-[8px] text-sm text-[#02475C] hover:bg-gray-50"
              disabled={deleteGroup.isPending}
            >
              {deleteGroup.isPending ? (
                <>
                  <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                  <span className="text-xs">Deleting...</span>
                </>
              ) : (
                "Delete"
              )}
            </Button>
            <Button
              type="button"
              onClick={handleDeleteCancel}
              className="flex h-[32px] w-[90px] items-center justify-center rounded-[8px] border-none bg-[linear-gradient(294.02deg,#02475C_0%,#1D8E89_100%)] px-[24px] py-[8px] text-sm text-white hover:opacity-90"
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Group;
