import React, { useState } from "react";
import { Search, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import IndividualDialog from "@/components/Template/individual";
import { EnhancedPagination } from "@/components/ui/pagination";
import FirstGroup from "./EmptyGroup";

const EditIcon = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.05 20.95H12.0063L19.0375 13.9187L18.0813 12.9625L11.05 19.9937V20.95ZM10.375 22.3C10.1875 22.3 10.025 22.2375 9.8875 22.1125C9.7625 21.975 9.7 21.8125 9.7 21.625V19.9937C9.7 19.8187 9.73125 19.65 9.79375 19.4875C9.85625 19.325 9.95625 19.175 10.0938 19.0375L19.0375 10.0937C19.175 9.95625 19.325 9.85625 19.4875 9.79375C19.65 9.73125 19.8188 9.7 19.9938 9.7C20.1688 9.7 20.3375 9.73125 20.5 9.79375C20.6625 9.85625 20.8125 9.95625 20.95 10.0937L21.9063 11.05C22.0438 11.1875 22.1438 11.3375 22.2063 11.5C22.2688 11.6625 22.3 11.8312 22.3 12.0062C22.3 12.1812 22.2688 12.35 22.2063 12.5125C22.1438 12.675 22.0438 12.825 21.9063 12.9625L12.9625 21.9062C12.825 22.0437 12.675 22.1437 12.5125 22.2062C12.35 22.2687 12.1813 22.3 12.0063 22.3H10.375ZM20.95 12.0062L19.9938 11.05L20.95 12.0062ZM18.55 13.45L18.0813 12.9625L19.0375 13.9187L18.55 13.45Z"
      fill="#667085"
    />
  </svg>
);

const DeleteIcon = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.85 22.3C12.475 22.3 12.1563 22.1687 11.8938 21.9062C11.6313 21.6437 11.5 21.325 11.5 20.95V11.95H11.275C11.0875 11.95 10.925 11.8875 10.7875 11.7625C10.6625 11.625 10.6 11.4625 10.6 11.275C10.6 11.0875 10.6625 10.9312 10.7875 10.8062C10.925 10.6687 11.0875 10.6 11.275 10.6H14.2V10.375C14.2 10.1875 14.2625 10.0312 14.3875 9.90625C14.525 9.76875 14.6875 9.7 14.875 9.7H17.125C17.3125 9.7 17.4688 9.76875 17.5938 9.90625C17.7313 10.0312 17.8 10.1875 17.8 10.375V10.6H20.725C20.9125 10.6 21.0688 10.6687 21.1938 10.8062C21.3313 10.9312 21.4 11.0875 21.4 11.275C21.4 11.4625 21.3313 11.625 21.1938 11.7625C21.0688 11.8875 20.9125 11.95 20.725 11.95H20.5V20.95C20.5 21.325 20.3688 21.6437 20.1063 21.9062C19.8438 22.1687 19.525 22.3 19.15 22.3H12.85ZM19.15 11.95H12.85V20.95H19.15V11.95ZM14.875 19.6C15.0625 19.6 15.2188 19.5375 15.3438 19.4125C15.4813 19.275 15.55 19.1125 15.55 18.925V13.975C15.55 13.7875 15.4813 13.6312 15.3438 13.5062C15.2188 13.3687 15.0625 13.3 14.875 13.3C14.6875 13.3 14.525 13.3687 14.3875 13.5062C14.2625 13.6312 14.2 13.7875 14.2 13.975V18.925C14.2 19.1125 14.2625 19.275 14.3875 19.4125C14.525 19.5375 14.6875 19.6 14.875 19.6ZM17.125 19.6C17.3125 19.6 17.4688 19.5375 17.5938 19.4125C17.7313 19.275 17.8 19.1125 17.8 18.925V13.975C17.8 13.7875 17.7313 13.6312 17.5938 13.5062C17.4688 13.3687 17.3125 13.3 17.125 13.3C16.9375 13.3 16.775 13.3687 16.6375 13.5062C16.5125 13.6312 16.45 13.7875 16.45 13.975V18.925C16.45 19.1125 16.5125 19.275 16.6375 19.4125C16.775 19.5375 16.9375 19.6 17.125 19.6ZM12.85 11.95V20.95V11.95Z"
      fill="#667085"
    />
  </svg>
);

const individualData = [
  {
    sn: 1,
    name: "Ramesh Yadav",
    phone: "+977 9812121212",
    email: "<EMAIL>",
    location: "Sudurpashim, Dadeldhura, Prashuram, Ward 1",
  },
  {
    sn: 2,
    name: "Ramesh Yadav",
    phone: "+977 9812121212",
    email: "<EMAIL>",
    location: "Sudurpashim, Dadeldhura, Prashuram, Ward 1",
  },
  {
    sn: 3,
    name: "Ramesh Yadav",
    phone: "+977 9812121212",
    email: "<EMAIL>",
    location: "Sudurpashim, Dadeldhura, Prashuram, Ward 1",
  },
  {
    sn: 4,
    name: "Ramesh Yadav",
    phone: "+977 9812121212",
    email: "<EMAIL>",
    location: "Sudurpashim, Dadeldhura, Prashuram, Ward 1",
  },
  {
    sn: 5,
    name: "Ramesh Yadav",
    phone: "+977 9812121212",
    email: "<EMAIL>",
    location: "Sudurpashim, Dadeldhura, Prashuram, Ward 1",
  },
  {
    sn: 6,
    name: "Ramesh Yadav",
    phone: "+977 9812121212",
    email: "<EMAIL>",
    location: "Sudurpashim, Dadeldhura, Prashuram, Ward 1",
  },
  {
    sn: 7,
    name: "Ramesh Yadav",
    phone: "+977 9812121212",
    email: "<EMAIL>",
    location: "Sudurpashim, Dadeldhura, Prashuram, Ward 1",
  },
  {
    sn: 8,
    name: "Ramesh Yadav",
    phone: "+977 9812121212",
    email: "<EMAIL>",
    location: "Sudurpashim, Dadeldhura, Prashuram, Ward 1",
  },
];

const Individual: React.FC = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading] = useState(false);

  const [showEmptyState, setShowEmptyState] = useState(true);

  const handleToggleEmptyState = () => {
    setShowEmptyState(!showEmptyState);
  };

  const filteredData = individualData.filter(
    (item) =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.location.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <div className="flex h-screen w-full flex-col bg-[#F5F5F5]">
      <div className="flex flex-1 flex-col gap-5 p-6">
        <div className="flex items-center justify-between">
          <h1 className="font-poppins text-xl leading-normal font-medium text-slate-900">
            Individual
          </h1>

          <div className="flex items-center gap-3">
            <div className="relative h-9 w-60">
              <Input
                type="text"
                placeholder="Search"
                className="font-poppins h-full w-full rounded-lg border border-[#d9d9d9] bg-white pr-3 pl-10 text-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
            </div>

            <IndividualDialog
              trigger={
                <Button className="poppins-regular h-10 cursor-pointer rounded-lg bg-gradient-to-l from-[#02475C] to-[#1D8E89] px-6 py-2 text-base leading-[100%] whitespace-nowrap text-white transition hover:opacity-90">
                  Add Individual
                </Button>
              }
              open={openDialog}
              setOpen={setOpenDialog}
            />

            <Button
              variant="outline"
              onClick={handleToggleEmptyState}
              className="border-gray-300 text-xs"
            >
              Toggle Demo Empty State
            </Button>
          </div>
        </div>

        <div className="flex flex-1 flex-col rounded-lg bg-white">
          <div className="overflow-hidden">
            {isLoading ? (
              <div className="flex h-64 items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-[#1D8E89]" />
                <span className="ml-2 text-lg text-gray-600">
                  Loading individuals...
                </span>
              </div>
            ) : showEmptyState ? (
              <FirstGroup />
            ) : filteredData.length === 0 ? (
              <div className="flex h-64 flex-col items-center justify-center text-center">
                <p className="mb-2 text-lg text-gray-600">
                  No individuals found
                </p>
                <p className="text-sm text-gray-500">
                  Try a different search term
                </p>
              </div>
            ) : (
              <Table className="my-4">
                <TableHeader>
                  <TableRow>
                    <TableHead>S.N. ↑</TableHead>
                    <TableHead>Name ↑</TableHead>
                    <TableHead>Phone ↑</TableHead>
                    <TableHead>Email ↑</TableHead>
                    <TableHead>Location ↑</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((item) => (
                    <TableRow key={item.sn}>
                      <TableCell>{item.sn}</TableCell>
                      <TableCell>{item.name}</TableCell>
                      <TableCell>{item.phone}</TableCell>
                      <TableCell>{item.email}</TableCell>
                      <TableCell>{item.location}</TableCell>
                      <TableCell>
                        <div className="flex h-[40px] w-[108px] items-center gap-2 px-3 py-2">
                          <div className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full hover:bg-[#e9f3f0]">
                            <EditIcon />
                          </div>
                          <div className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full hover:bg-[#e9f3f0]">
                            <DeleteIcon />
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>
        </div>
      </div>

      {!showEmptyState && filteredData.length > 0 && (
        <div className="mx-6 mb-3 flex-none">
          <EnhancedPagination
            currentPage={1}
            totalPages={Math.ceil(filteredData.length / 10)}
            itemsPerPage={10}
            totalItems={filteredData.length}
            onPageChange={() => {}}
            onItemsPerPageChange={() => {}}
          />
        </div>
      )}
    </div>
  );
};

export default Individual;
