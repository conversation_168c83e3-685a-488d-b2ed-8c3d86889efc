"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import Group from "./Group";
import Individual from "./Individual";

export default function Groups() {
  return (
    <div className="h-full bg-[#F5F5F5]">
      <Tabs defaultValue="group" className="h-full">
        <div className="border-b bg-white px-6 py-3">
          <TabsList className="grid w-[250px] grid-cols-2">
            <TabsTrigger value="group">Groups</TabsTrigger>
            <TabsTrigger value="individual">Individuals</TabsTrigger>
          </TabsList>
        </div>
        <TabsContent value="group" className="h-[calc(100%-64px)]">
          <Group />
        </TabsContent>
        <TabsContent value="individual" className="h-[calc(100%-64px)]">
          <Individual />
        </TabsContent>
      </Tabs>
    </div>
  );
}
