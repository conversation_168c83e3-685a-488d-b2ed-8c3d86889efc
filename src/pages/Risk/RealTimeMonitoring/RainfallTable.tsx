import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { getStatusColor, TableProps } from "../RealTimeMonitoring/utils";
import { useEffect, useMemo } from "react";
import { Loader2 } from "lucide-react";
import { useRainfallDataPaginated } from "@/api/mapData";
import { PaginationParams } from "@/lib/utils";
import { useTranslation } from "react-i18next";

interface FormattedRainfallData {
  sn: number;
  basin: string;
  index: string | null;
  station: string;
  hr1: number;
  hr3: number;
  hr6: number;
  hr12: number;
  hr24: number;
  status: string;
}

interface ExtendedTableProps extends TableProps {
  updatePaginationInfo?: (totalItems: number, totalPages: number) => void;
  updateSummaryData?: (data: FormattedRainfallData[]) => void;
}

export const RainfallTable = ({
  searchTerm,
  currentPage,
  itemsPerPage,
  updatePaginationInfo,
  updateSummaryData,
}: ExtendedTableProps) => {
  const { t } = useTranslation();

  const mapStatus = (status: string): string => {
    switch (status?.toUpperCase()) {
      case "DANGER LEVEL":
      case "DANGER":
        return t("danger");
      case "WARNING LEVEL":
      case "WARNING":
        return t("warning");
      case "BELOW WARNING LEVEL":
      default:
        return t("belowWarning");
    }
  };

  const paginationParams: PaginationParams = {
    page: currentPage,
    page_size: itemsPerPage,
  };

  const {
    data: response,
    isLoading,
    error,
    isError,
  } = useRainfallDataPaginated(paginationParams);

  const rainfallData: FormattedRainfallData[] = useMemo(
    () =>
      response?.results.map((station, index) => ({
        sn: (currentPage - 1) * itemsPerPage + index + 1,
        basin: station.basin || t("unknown"),
        index: station.stationIndex || null,
        station: station.name || t("unknownStation"),
        hr1: station.rainfall1h || 0,
        hr3: station.rainfall3h || 0,
        hr6: station.rainfall6h || 0,
        hr12: station.rainfall12h || 0,
        hr24: station.rainfall24h || 0,
        status: mapStatus(station.status),
      })) || [],
    [response, currentPage, itemsPerPage, t],
  );

  useEffect(() => {
    if (response) {
      const actualCount =
        response.count > 0
          ? response.count
          : response.results.length > 0 && response.next
            ? currentPage * itemsPerPage + 1
            : response.results.length;

      const totalPages =
        response.count > 0
          ? Math.ceil(response.count / itemsPerPage)
          : response.next
            ? currentPage + 1
            : currentPage;

      updatePaginationInfo?.(actualCount, totalPages);
      updateSummaryData?.(rainfallData);
    }
  }, [
    response,
    currentPage,
    itemsPerPage,
    rainfallData,
    updatePaginationInfo,
    updateSummaryData,
  ]);

  const filteredData = rainfallData.filter(
    (item) =>
      item.station.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.basin?.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const displayData = filteredData;

  return (
    <div className="h-full w-full">
      {isLoading ? (
        <div className="flex h-40 items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-[#1D8E89]" />
          <span className="ml-2">{t("loadingRainfallData")}</span>
        </div>
      ) : isError ? (
        <div className="p-4 text-center text-red-500">
          <p>
            {error instanceof Error
              ? error.message
              : t("failedToFetchRainfall")}
          </p>
          <p className="mt-2 text-sm">{t("pleaseRetryLater")}</p>
        </div>
      ) : (
        <div className="h-full overflow-auto">
          <Table className="w-full table-fixed">
            <TableHeader className="sticky top-0 z-10 bg-white shadow-sm">
              <TableRow>
                <TableHead className="w-16 text-center">
                  {t("serialNumber")} ↑
                </TableHead>
                <TableHead className="w-32">{t("basinName")} ↑</TableHead>
                <TableHead className="w-24 text-center">
                  {t("stationIndex")} ↑
                </TableHead>
                <TableHead className="w-48">{t("stationName")} ↑</TableHead>
                <TableHead className="w-20 text-center">{t("1hr")} ↑</TableHead>
                <TableHead className="w-20 text-center">{t("3hr")} ↑</TableHead>
                <TableHead className="w-20 text-center">{t("6hr")} ↑</TableHead>
                <TableHead className="w-20 text-center">
                  {t("12hr")} ↑
                </TableHead>
                <TableHead className="w-20 text-center">
                  {t("24hr")} ↑
                </TableHead>
                <TableHead className="w-32 text-center">
                  {t("tableStatus")} ↑
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayData.length > 0 ? (
                displayData.map((item) => (
                  <TableRow
                    key={item.sn}
                    className={item.status === "Danger" ? "bg-[#FFF5F3]" : ""}
                  >
                    <TableCell className="w-16 text-center">
                      {item.sn}
                    </TableCell>
                    <TableCell className="w-32">{item.basin}</TableCell>
                    <TableCell className="w-24 text-center">
                      {item.index ?? "-"}
                    </TableCell>
                    <TableCell className="w-48">{item.station}</TableCell>
                    <TableCell className="w-20 text-center">
                      {item.hr1}
                    </TableCell>
                    <TableCell className="w-20 text-center">
                      {item.hr3}
                    </TableCell>
                    <TableCell className="w-20 text-center">
                      {item.hr6}
                    </TableCell>
                    <TableCell className="w-20 text-center">
                      {item.hr12}
                    </TableCell>
                    <TableCell className="w-20 text-center">
                      {item.hr24}
                    </TableCell>
                    <TableCell
                      className={`w-32 text-center ${getStatusColor(item.status)}`}
                    >
                      {item.status}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={10} className="py-4 text-center">
                    {t("noDataFound")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};
