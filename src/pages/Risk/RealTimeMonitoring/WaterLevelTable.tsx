import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { getStatusColor, TableProps } from "../RealTimeMonitoring/utils";
import { useEffect, useMemo } from "react";
import { Loader2 } from "lucide-react";
import { useWaterLevelDataPaginated } from "@/api/mapData";
import { PaginationParams } from "@/lib/utils";
import { useTranslation } from "react-i18next";

interface FormattedWaterLevelData {
  sn: number;
  basin: string;
  index: string | null;
  station: string;
  level: number;
  warningLevel: number;
  dangerLevel: string;
  status: string;
}

interface ExtendedTableProps extends TableProps {
  updatePaginationInfo?: (totalItems: number, totalPages: number) => void;
  updateSummaryData?: (data: FormattedWaterLevelData[]) => void;
}

export const WaterLevelTable = ({
  searchTerm,
  currentPage,
  itemsPerPage,
  updatePaginationInfo,
  updateSummaryData,
}: ExtendedTableProps) => {
  const { t } = useTranslation();

  const mapStatus = (status: string): string => {
    switch (status?.toUpperCase()) {
      case "DANGER LEVEL":
      case "DANGER":
        return t("danger");
      case "WARNING LEVEL":
      case "WARNING":
        return t("warning");
      case "BELOW WARNING LEVEL":
      default:
        return t("belowWarning");
    }
  };

  const paginationParams: PaginationParams = {
    page: currentPage,
    page_size: itemsPerPage,
  };

  const {
    data: response,
    isLoading,
    error,
    isError,
  } = useWaterLevelDataPaginated(paginationParams);

  const waterLevelData: FormattedWaterLevelData[] = useMemo(
    () =>
      response?.results.map((station, index) => ({
        sn: (currentPage - 1) * itemsPerPage + index + 1,
        basin: station.basin || t("unknown"),
        index: station.stationIndex || null,
        station: station.name || t("unknownStation"),
        level: station.value || 0,
        warningLevel: station.warningLevel || 0,
        dangerLevel: station.dangerLevel?.toString() || t("noWeatherData"),
        status: mapStatus(station.status),
      })) || [],
    [response, currentPage, itemsPerPage, t],
  );

  useEffect(() => {
    if (response) {
      const actualCount =
        response.count > 0
          ? response.count
          : response.results.length > 0 && response.next
            ? currentPage * itemsPerPage + 1
            : response.results.length;

      const totalPages =
        response.count > 0
          ? Math.ceil(response.count / itemsPerPage)
          : response.next
            ? currentPage + 1
            : currentPage;

      updatePaginationInfo?.(actualCount, totalPages);
      updateSummaryData?.(waterLevelData);
    }
  }, [
    response,
    currentPage,
    itemsPerPage,
    waterLevelData,
    updatePaginationInfo,
    updateSummaryData,
  ]);

  const filteredData = waterLevelData.filter(
    (item) =>
      item.station.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.basin?.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const displayData = filteredData;

  return (
    <div className="h-full w-full">
      {isLoading ? (
        <div className="flex h-40 items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-[#1D8E89]" />
          <span className="ml-2">{t("loadingWaterLevelData")}</span>
        </div>
      ) : isError ? (
        <div className="p-4 text-center text-red-500">
          <p>
            {error instanceof Error
              ? error.message
              : t("failedToFetchWaterLevel")}
          </p>
          <p className="mt-2 text-sm">{t("pleaseRetryLater")}</p>
        </div>
      ) : (
        <div className="h-full overflow-auto">
          <Table className="w-full table-fixed">
            <TableHeader className="sticky top-0 z-10 bg-white shadow-sm">
              <TableRow>
                <TableHead className="w-16 text-center">
                  {t("serialNumber")} ↑
                </TableHead>
                <TableHead className="w-32">{t("basinName")} ↑</TableHead>
                <TableHead className="w-24 text-center">
                  {t("stationIndex")} ↑
                </TableHead>
                <TableHead className="w-48">{t("stationName")} ↑</TableHead>
                <TableHead className="w-32 text-center">
                  {t("waterLevelM")} ↑
                </TableHead>
                <TableHead className="w-32 text-center">
                  {t("warningLevelM")} ↑
                </TableHead>
                <TableHead className="w-32 text-center">
                  {t("dangerLevelM")} ↑
                </TableHead>
                <TableHead className="w-32 text-center">
                  {t("tableStatus")} ↑
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayData.length > 0 ? (
                displayData.map((item) => (
                  <TableRow
                    key={item.sn}
                    className={item.status === "Danger" ? "bg-[#FFF5F3]" : ""}
                  >
                    <TableCell className="w-16 text-center">
                      {item.sn}
                    </TableCell>
                    <TableCell className="w-32">{item.basin}</TableCell>
                    <TableCell className="w-24 text-center">
                      {item.index ?? "-"}
                    </TableCell>
                    <TableCell className="w-48">{item.station}</TableCell>
                    <TableCell className="w-32 text-center">
                      {item.level}
                    </TableCell>
                    <TableCell className="w-32 text-center">
                      {item.warningLevel}
                    </TableCell>
                    <TableCell className="w-32 text-center">
                      {item.dangerLevel}
                    </TableCell>
                    <TableCell
                      className={`w-32 text-center ${getStatusColor(item.status)}`}
                    >
                      {item.status}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="py-4 text-center">
                    {t("noDataFound")}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};
