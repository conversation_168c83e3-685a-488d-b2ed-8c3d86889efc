import { Tabs, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { Command, CommandInput } from "@/components/ui/command";
import { useTranslation } from "react-i18next";

import { EnhancedPagination } from "@/components/ui/pagination";
import { WaterLevelTable } from "../RealTimeMonitoring/WaterLevelTable";
import { RainfallTable } from "../RealTimeMonitoring/RainfallTable";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Channel } from "@/types/sendAlert";
import { useStatusCount } from "@/hooks/useStatusCount";
import { StatusCount } from "@/api/mapData";

interface ExtendedStatusCount extends StatusCount {
  WARNING?: number;
  DANGER?: number;
}

export default function RealTimeData() {
  const [searchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<"water-level" | "rainfall">(
    "water-level",
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const { t } = useTranslation();

  useEffect(() => {
    const tabParam = searchParams.get("tab");
    if (tabParam === "rainfall" || tabParam === "water-level") {
      setActiveTab(tabParam);
    }
  }, [searchParams]);

  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const statusCountHook = useStatusCount();

  const updatePaginationInfo = (items: number, pages?: number) => {
    setTotalItems(items);
    setTotalPages(pages || Math.ceil(items / itemsPerPage));
  };

  const handlePageChange = (page: number) => setCurrentPage(page);
  const handleItemsPerPageChange = (items: number) => {
    setItemsPerPage(items);
    setCurrentPage(1);

    if (totalItems > 0) {
      setTotalPages(Math.ceil(totalItems / items));
    }
  };

  const handleTabChange = (tab: "water-level" | "rainfall") => {
    setActiveTab(tab);
    setCurrentPage(1);
    setTotalItems(0);
    setTotalPages(1);
  };

  const getCurrentStatusCounts = (): ExtendedStatusCount => {
    if (activeTab === "rainfall") {
      return statusCountHook.rainfallStatusCount as ExtendedStatusCount;
    } else {
      return statusCountHook.waterLevelStatusCount as ExtendedStatusCount;
    }
  };

  const currentStatusCounts = getCurrentStatusCounts();

  const summaryData = [
    {
      titleKey: "totalStations",
      count:
        (currentStatusCounts["BELOW WARNING LEVEL"] || 0) +
        ((currentStatusCounts["WARNING LEVEL"] || 0) +
          (currentStatusCounts["WARNING"] || 0)) +
        ((currentStatusCounts["DANGER LEVEL"] || 0) +
          (currentStatusCounts["DANGER"] || 0)),
      icon: "chat",
      img: "/Station.svg",
      channel: Channel.SMS,
    },
    {
      titleKey: "belowWarning",
      count: currentStatusCounts["BELOW WARNING LEVEL"] || 0,
      icon: "mail",
      img: "/Below.svg",
      channel: Channel.EMAIL,
    },
    {
      titleKey: "warning",
      count:
        (currentStatusCounts["WARNING LEVEL"] || 0) +
        (currentStatusCounts["WARNING"] || 0),
      icon: "warning",
      img: "/Warn.svg",
      channel: null,
    },
    {
      titleKey: "danger",
      count:
        (currentStatusCounts["DANGER LEVEL"] || 0) +
        (currentStatusCounts["DANGER"] || 0),
      icon: "increase",
      img: "/Up.svg",
      channel: null,
    },
  ];

  const updateSummaryData = () => {};

  return (
    <div className="flex h-screen w-full flex-col overflow-hidden bg-[#F5F5F5]">
      <div className="flex-none p-6 pb-3">
        <div className="flex items-center justify-between">
          <h1 className="heading-semibold text-xl font-semibold text-[#232323]">
            {t("realTimeMonitoringTitle")}
          </h1>

          <div className="flex items-center gap-2">
            <Tabs
              value={activeTab}
              onValueChange={(value) => {
                handleTabChange(value as "water-level" | "rainfall");
              }}
              className="inline-flex items-start rounded-none bg-[#E9F3F0] p-1"
            >
              <TabsList className="flex gap-1 bg-transparent p-0 px-0">
                <TabsTrigger
                  value="water-level"
                  className="flex-[0_0_auto] px-3 py-0 text-xs font-normal transition-colors data-[state=active]:bg-[#02475C] data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-[#02475C]"
                >
                  {t("riverWatch")}
                </TabsTrigger>
                <TabsTrigger
                  value="rainfall"
                  className="flex-[0_0_auto] px-3 py-0 text-xs font-normal transition-colors data-[state=active]:bg-[#02475C] data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-[#02475C]"
                >
                  {t("rainfallWatch")}
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="relative z-50 w-64">
              <Command className="rounded-lg border">
                <CommandInput
                  placeholder={t("search")}
                  onValueChange={(value) => {
                    setSearchTerm(value);
                    setCurrentPage(1);
                  }}
                />
              </Command>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-none p-6 pb-3">
        <div className="flex gap-4">
          {summaryData.map((item, i) => (
            <Card
              key={i}
              className="flex h-[70px] flex-1 flex-row gap-3 rounded-[12px] p-2"
            >
              <img src={item.img} className="h-12 w-12" />
              <div className="flex w-full flex-col">
                <CardHeader className="p-0 pb-1">
                  <CardTitle className="poppins-regular text-[12px] text-[#475467]">
                    {t(item.titleKey)}
                  </CardTitle>
                </CardHeader>
                <CardContent
                  className={`poppins-semibold p-0 text-[17px] ${item.titleKey === "belowWarning" ? "text-[#208661]" : item.titleKey === "warning" ? "text-[#FFAE19]" : item.titleKey === "danger" ? "text-[#E13636]" : "text-[#232323]"}`}
                >
                  {item.count}
                </CardContent>
              </div>
            </Card>
          ))}
        </div>
      </div>

      <div className="flex min-h-0 flex-grow flex-col px-6 pb-3">
        <div className="flex min-h-0 flex-grow flex-col overflow-hidden rounded-lg bg-white">
          <div className="relative min-h-0 flex-grow overflow-y-auto">
            {activeTab === "water-level" ? (
              <WaterLevelTable
                searchTerm={searchTerm}
                currentPage={currentPage}
                itemsPerPage={itemsPerPage}
                updatePaginationInfo={updatePaginationInfo}
                updateSummaryData={updateSummaryData}
              />
            ) : (
              <RainfallTable
                searchTerm={searchTerm}
                currentPage={currentPage}
                itemsPerPage={itemsPerPage}
                updatePaginationInfo={updatePaginationInfo}
                updateSummaryData={updateSummaryData}
              />
            )}
          </div>
        </div>
      </div>

      <div className="mr-6 mb-4 ml-6 flex items-center justify-between text-sm text-[#667085]">
        {activeTab === "rainfall" ? (
          <div className="flex-start flex">
            <p className="Lato-regular text-[14px] text-[#E13636]">
              {t("warningNote")}
            </p>
            <p className="Lato-regular text-[14px] text-[#667085]">
              <span className="px-2">|</span>
              <span className="px-2">{t("mmIn1hr")}</span>
              <span className="px-2">|</span>
              <span className="px-2">{t("mmIn3hr")}</span>
              <span className="px-2">|</span>
              <span className="px-2">{t("mmIn6hr")}</span>
              <span className="px-2">|</span>
              <span className="px-2">{t("mmIn12hr")}</span>
              <span className="px-2">|</span>
              <span className="px-2">{t("mmIn24hr")}</span>
            </p>
          </div>
        ) : (
          <div></div>
        )}
        <div className="flex items-center">
          <span className="Lato-regular text-[14px] text-[#46556a]">
            {t("dataSource")}:
          </span>
          <a
            href="https://www.dhm.gov.np/"
            target="_blank"
            rel="noopener noreferrer"
            className="ml-3 cursor-pointer text-[14px] font-semibold text-[#1D8E89] transition-colors hover:text-[#156B66]"
          >
            DHM
          </a>
        </div>
      </div>

      {totalItems > 0 && (
        <div className="mb-3 ml-3 flex-none">
          <EnhancedPagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      )}
    </div>
  );
}
