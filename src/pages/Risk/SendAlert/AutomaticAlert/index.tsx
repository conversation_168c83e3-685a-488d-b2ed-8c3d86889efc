import React, { useRef, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { MessageSection } from "../components/MessageSection";
import { SuccessDialog } from "../components/SuccessDialog";
import { Channel, MessageType, FileUploadProps } from "../types";
import ThresholdRange from "../components/ThresholdRange";

export default function ManualAlert() {
  const [channel, setChannel] = useState<Channel>("sms");
  const [messageType, setMessageType] = useState<MessageType>("template");
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  const handleFileClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files);
      setSelectedFiles((prev) => [...prev, ...newFiles]);

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragOver(false);
    const files = event.dataTransfer.files;
    if (files) {
      const newFiles = Array.from(files);
      setSelectedFiles((prev) => [...prev, ...newFiles]);
    }
  };

  const handleFileDelete = (fileToDelete: File) => {
    setSelectedFiles((prev) => prev.filter((file) => file !== fileToDelete));
  };

  const fileUploadProps = {
    selectedFiles,
    dragOver,
    onFileClick: handleFileClick,
    onFileChange: handleFileChange,
    onDrop: handleDrop,
    onDragOver: (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      setDragOver(true);
    },
    onDragLeave: (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      setDragOver(false);
    },
    onFileDelete: handleFileDelete,
    fileInputRef,
  };

  return (
    <div className="flex h-screen w-full flex-col bg-[#F5F5F5]">

      <div className="px-10 py-5">
        <h1 className="poppins-medium text-xl text-[#434343]">
          Send Automatic Alert
        </h1>
      </div>


      <div className="flex flex-1 justify-center overflow-y-auto px-10">
        <Card className="h-fit w-[546px] flex-col justify-center gap-[32px] rounded-xl bg-white p-[20px] shadow-none">
          <CardContent className="flex w-[506px] flex-col items-start gap-6 p-0">

            <div className="flex w-full flex-col items-start gap-1">
              <div className="flex w-full items-center gap-1">
                <div className="poppins text-sm leading-[18px] text-[#434343]">
                  <span>Hazard Type </span>
                  <span className="text-[#d92525]">*</span>
                </div>
              </div>

              <Select>
                <SelectTrigger className="font-poppins relative h-10 w-full rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                  <SelectValue
                    placeholder="Choose Hazard Type"
                    className="font-['Lato'] text-[14px] leading-[18px] font-normal tracking-[0] text-[#555555] placeholder-[#9D9D9D]"
                  />
                  <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555]">
                    arrow_drop_down
                  </span>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fire">Fire</SelectItem>
                  <SelectItem value="flood">Flood</SelectItem>
                  <SelectItem value="drought">Drought</SelectItem>
                  <SelectItem value="landslide">Landslide</SelectItem>
                </SelectContent>
              </Select>
            </div>


            <div className="flex w-full flex-col items-start gap-1">
              <div className="flex w-full items-center gap-1">
                <div className="poppins text-sm leading-[18px] text-[#434343]">
                  <span>Area </span>
                  <span className="text-[#d92525]">*</span>
                </div>
              </div>

              <Select>
                <SelectTrigger className="font-poppins relative h-10 w-full rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                  <SelectValue
                    placeholder="Choose  Area"
                    className="font-['Lato'] text-[14px] leading-[18px] font-normal tracking-[0] text-[#9D9D9D] placeholder-[#9D9D9D]"
                  />
                  <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555]">
                    arrow_drop_down
                  </span>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="lumbini">Lumbini</SelectItem>
                  <SelectItem value="kathmandu">Kathmandu</SelectItem>
                  <SelectItem value="bharatpur">Bharatpur</SelectItem>
                  <SelectItem value="chitwan">Chitwan</SelectItem>
                </SelectContent>
              </Select>
            </div>


            <div className="flex w-full flex-col items-start gap-1">
              <div className="flex w-full items-center gap-1">
                <div className="poppins text-sm leading-[18px] text-[#434343]">
                  <span>Threshold Value </span>
                  <span className="text-[#d92525]">*</span>
                </div>
              </div>

              <Select>
                <SelectTrigger className="font-poppins relative h-10 w-full rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                  <SelectValue
                    placeholder="Choose Threshold Value"
                    className="font-['Lato'] text-[14px] leading-[18px] font-normal tracking-[0] text-[#9D9D9D] placeholder-[#9D9D9D]"
                  />
                  <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555]">
                    arrow_drop_down
                  </span>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">0</SelectItem>
                  <SelectItem value="1">1</SelectItem>
                  <SelectItem value="2">2</SelectItem>
                  <SelectItem value="3">3</SelectItem>
                </SelectContent>
              </Select>
            </div>


            <ThresholdRange />


            <div className="flex flex-col items-start gap-5">

              <div className="flex flex-col items-start gap-2">
                <div className="flex w-full flex-col items-start gap-1">
                  <div className="flex w-full items-center gap-1">
                    <div className="poppins text-sm leading-[18px] text-[#434343]">
                      <span>Channel </span>
                      <span className="text-[#d92525]">*</span>
                    </div>
                  </div>
                </div>

                <ToggleGroup
                  type="single"
                  value={channel}
                  onValueChange={(value: string) =>
                    value && setChannel(value as Channel)
                  }
                  className="inline-flex items-center rounded bg-[#F5F5F5] p-0.5 h-9 w-[109px]"
                >
                  <ToggleGroupItem
                    value="sms"
                    className="flex-1 h-6 rounded-sm border-1 border-solid border-[#d0d5dd] px-2 py-0 text-xs data-[state=off]:border-none data-[state=off]:text-[#757575] data-[state=on]:border-[#02475C] data-[state=on]:bg-[#E9F3F0] data-[state=on]:text-[#02475C]"
                  >
                    <span className="font-[600] whitespace-nowrap text-xs">SMS</span>
                  </ToggleGroupItem>

                  <ToggleGroupItem
                    value="email"
                    className="flex-1 h-6 rounded-sm border-1 border-solid border-[#d0d5dd] px-2 py-0 text-xs data-[state=off]:border-none data-[state=off]:text-[#757575] data-[state=on]:border-[#02475C] data-[state=on]:bg-[#E9F3F0] data-[state=on]:text-[#02475C]"
                  >
                    <span className="font-[600] whitespace-nowrap text-xs">Email</span>
                  </ToggleGroupItem>
                </ToggleGroup>
              </div>


              <div className="flex flex-col items-start gap-2">
                <div className="flex w-full flex-col items-start gap-1">
                  <div className="flex w-full items-center gap-1">
                    <div className="poppins text-sm leading-[18px] text-[#434343]">
                      <span>Alert Message </span>
                      <span className="text-[#d92525]">*</span>
                    </div>
                  </div>
                </div>

                <ToggleGroup
                  type="single"
                  value={messageType}
                  onValueChange={(value: string) =>
                    value && setMessageType(value as MessageType)
                  }
                  className="inline-flex items-center rounded bg-[#F5F5F5] p-0.5 h-9 w-[240px]"
                >
                  <ToggleGroupItem
                    value="custom"
                    className="flex-1 h-6 rounded-sm border-1 border-solid border-[#d0d5dd] px-2 py-0 text-xs data-[state=off]:border-none data-[state=off]:text-[#757575] data-[state=on]:border-[#02475C] data-[state=on]:bg-[#E9F3F0] data-[state=on]:text-[#02475C]"
                  >
                    <span className="font-[600] whitespace-nowrap text-xs">
                      Custom message
                    </span>
                  </ToggleGroupItem>

                  <ToggleGroupItem
                    value="template"
                    className="flex-1 h-6 rounded-sm border-1 border-solid border-[#d0d5dd] px-2 py-0 text-xs data-[state=off]:border-none data-[state=off]:text-[#757575] data-[state=on]:border-[#02475C] data-[state=on]:bg-[#E9F3F0] data-[state=on]:text-[#02475C]"
                  >
                    <span className="font-[600] whitespace-nowrap text-xs">
                      Template
                    </span>
                  </ToggleGroupItem>
                </ToggleGroup>
              </div>
            </div>


            <MessageSection
              channel={channel}
              messageType={messageType}
              fileUploadProps={fileUploadProps as FileUploadProps}
            />
          </CardContent>


          <div className="mt-9 flex justify-center">
            <Button
              onClick={() => setShowSuccessDialog(true)}
              className="cursor-pointer rounded-md bg-gradient-to-r from-[#00796B] to-[#004D40] px-5 py-2 text-white transition hover:opacity-90"
            >
              Send Alert <img className="ml-2 h-4 w-4" src="/send_white.svg" />
            </Button>
          </div>
        </Card>
      </div>


      <SuccessDialog
        isOpen={showSuccessDialog}
        onClose={() => setShowSuccessDialog(false)}
      />
    </div>
  );
}
