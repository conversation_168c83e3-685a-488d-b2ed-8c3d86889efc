import React, { useRef, useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { MessageSection } from "../components/MessageSection";
import { SuccessDialog } from "../components/SuccessDialog";
import { Channel, MessageType, FileUploadProps, HazardType } from "../types";
import { useAlertTemplate } from "@/api/alertTemplates";
import { useCreateSendAlert } from "@/api/sendAlert";
import {
  SendAlertRequest,
  HazardType as ApiHazardType,
  Channel as ApiChannel,
  TemplateType as ApiTemplateType,
} from "@/types/sendAlert";
import { toast } from "sonner";
import { useLocation } from "react-router-dom";

export default function ManualAlert() {
  const location = useLocation();

  const [channel, setChannel] = useState<Channel>("sms");
  const [messageType, setMessageType] = useState<MessageType>("custom");
  const [hazardType, setHazardType] = useState<HazardType>("flood");
  const [mailSubject, setMailSubject] = useState("");
  const [alertMessage, setAlertMessage] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [targetGroupId, setTargetGroupId] = useState<number | null>(null);
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(
    null,
  );
  const [dragOver, setDragOver] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [isLoadingMedia, setIsLoadingMedia] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [errors, setErrors] = useState<{
    hazardType?: string;
    alertMessage?: string;
    mailSubject?: string;
    targetGroupId?: string;
  }>({});

  const fileInputRef = useRef<HTMLInputElement>(null);

  const createSendAlertMutation = useCreateSendAlert();
  const { data: templateData } = useAlertTemplate(selectedTemplateId || 0);

  const resetForm = () => {
    if (messageType === "custom") {
      setAlertMessage("");
      setMailSubject("");
      setSelectedFiles([]);
    }
    setTargetGroupId(null);
  };

  useEffect(() => {
    if (!templateData) return;

    setHazardType(templateData.hazard_type as HazardType);
    setAlertMessage(templateData.alert_message);
    if (templateData.mail_subject) {
      setMailSubject(templateData.mail_subject);
    }

    if (templateData.media && channel === "email") {
      const fetchAttachment = async () => {
        try {
          setIsLoadingMedia(true);

          const url = new URL(templateData.media.file);
          const relativePath = url.pathname; 
          const res = await fetch(relativePath);
          const blob = await res.blob();
          const file = new File(
            [blob],
            templateData.media.title || "attachment",
            { type: blob.type },
          );
          setSelectedFiles([file]);
          toast.success("Template attachment loaded successfully");
        } catch {
          toast.error("Failed to download attachment from template");
        } finally {
          setIsLoadingMedia(false);
        }
      };
      fetchAttachment();
    } else {
      setSelectedFiles([]);
    }
  }, [templateData, channel]);

  useEffect(() => {
    const navigationState = location.state as {
      templateData?: {
        id: number;
        template_name: string;
        alert_message: string;
        mail_subject?: string;
        hazard_type: string;
        template_type: string;
        target_group?: number;
      };
      autoFill?: boolean;
    };

    if (navigationState?.autoFill && navigationState?.templateData) {
      const template = navigationState.templateData;

      setHazardType(template.hazard_type as HazardType);
      setChannel(template.template_type.toLowerCase() as Channel);
      setMessageType("template");
      setSelectedTemplateId(template.id);
      setAlertMessage(template.alert_message || "");

      if (template.mail_subject) {
        setMailSubject(template.mail_subject);
      }

      if (template.target_group) {
        setTargetGroupId(template.target_group);
      }

      toast.success(`Template "${template.template_name}" loaded successfully`);
    }
  }, [location.state]);

  const handleFileClick = () => fileInputRef.current?.click();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    const newFiles = Array.from(e.target.files);
    setSelectedFiles((prev) => [...prev, ...newFiles]);
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragOver(false);
    const newFiles = Array.from(e.dataTransfer.files);
    setSelectedFiles((prev) => [...prev, ...newFiles]);
  };

  const handleFileDelete = (fileToDelete: File) => {
    setSelectedFiles((prev) => prev.filter((file) => file !== fileToDelete));
  };
  const fileUploadProps: FileUploadProps = {
    selectedFiles,
    dragOver,
    onFileClick: handleFileClick,
    onFileChange: handleFileChange,
    onDrop: handleDrop,
    onDragOver: (e) => {
      e.preventDefault();
      setDragOver(true);
    },
    onDragLeave: (e) => {
      e.preventDefault();
      setDragOver(false);
    },
    onFileDelete: handleFileDelete,
    fileInputRef: fileInputRef as React.RefObject<HTMLInputElement>,
    isLoading: isLoadingMedia,
  };

  const handleSendAlert = async () => {
    if (!validateForm()) {
      return;
    }

    const alertData: SendAlertRequest = {
      hazard_type: hazardType as ApiHazardType,
      channel: channel as ApiChannel,
      template_type:
        messageType === "template"
          ? ApiTemplateType.TEMPLATE
          : ApiTemplateType.CUSTOM,
      alert_message: alertMessage,
      mail_subject: channel === "email" ? mailSubject : undefined,
      file_upload: selectedFiles[0],
      template:
        messageType === "template"
          ? selectedTemplateId || undefined
          : undefined,
      target_group: targetGroupId || undefined,
    };

    try {
      setIsSending(true);
      await createSendAlertMutation.mutateAsync(alertData);
      setShowSuccessDialog(true);
      resetForm();
      toast.success("Alert sent successfully");
    } catch {
      toast.error("Failed to send alert");
    } finally {
      setIsSending(false);
    }
  };

  const validateForm = () => {
    const newErrors: {
      hazardType?: string;
      alertMessage?: string;
      mailSubject?: string;
      targetGroupId?: string;
    } = {};
    if (!hazardType) {
      newErrors.hazardType = "Hazard type is required";
    }
    if (!alertMessage.trim()) {
      newErrors.alertMessage = "Alert message is required";
    }
    if (!targetGroupId) {
      newErrors.targetGroupId = "Target group is required";
    }
    if (channel === "email" && !mailSubject.trim()) {
      newErrors.mailSubject = "Mail subject is required for email channel";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  return (
    <div className="flex h-full w-full flex-col bg-[#F5F5F5]">

      <div className="px-10 py-5">
        <h1 className="poppins-medium text-xl text-[#434343]">Send Alert</h1>
      </div>


      <div className="flex flex-1 justify-center overflow-y-auto px-10">
        <Card className="h-fit w-[546px] flex-col justify-center gap-[32px] rounded-xl bg-white p-[20px] shadow-none">
          <CardContent className="flex w-[506px] flex-col items-start gap-6 p-0">

            <div className="w-full">
              <label className="poppins text-sm text-[#434343]">
                Hazard Type <span className="text-[#d92525]">*</span>
              </label>
              <Select
                value={hazardType}
                onValueChange={(val) => {
                  setHazardType(val as HazardType);
                  if (val)
                    setErrors((prev) => ({ ...prev, hazardType: undefined }));
                }}
              >
                <SelectTrigger className="font-poppins relative h-10 w-full rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                  <SelectValue
                    placeholder="Choose Hazard Type"
                    className="text-sm font-semibold text-[#9d9d9d]"
                  />
                  <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555]">
                    arrow_drop_down
                  </span>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="flood">Flood</SelectItem>
                  <SelectItem value="landslide">Landslide</SelectItem>
                  <SelectItem value="drought">Drought</SelectItem>
                  <SelectItem value="fire">Fire</SelectItem>
                </SelectContent>
              </Select>
              {errors.hazardType && (
                <p className="mt-1 text-xs text-red-500">{errors.hazardType}</p>
              )}
            </div>


            <ToggleGroup
              type="single"
              value={channel}
              onValueChange={(val) => val && setChannel(val as Channel)}
              className="inline-flex items-center rounded bg-[#F5F5F5] p-0.5 h-9 w-[109px]"
            >
              {["sms", "email"].map((ch) => (
                <ToggleGroupItem
                  key={ch}
                  value={ch}
                  className="flex-1 h-6 rounded-sm border-1 border-solid border-[#d0d5dd] px-2 py-0 text-xs data-[state=off]:border-none data-[state=off]:text-[#757575] data-[state=on]:border-[#02475C] data-[state=on]:bg-[#E9F3F0] data-[state=on]:text-[#02475C]"
                >
                  <span className="font-[600] whitespace-nowrap text-xs">{ch.toUpperCase()}</span>
                </ToggleGroupItem>
              ))}
            </ToggleGroup>


            <ToggleGroup
              type="single"
              value={messageType}
              onValueChange={(val) => val && setMessageType(val as MessageType)}
              className="inline-flex items-center rounded bg-[#F5F5F5] p-0.5 h-9 w-[240px]"
            >
              <ToggleGroupItem
                value="custom"
                className="flex-1 h-6 rounded-sm border-1 border-solid border-[#d0d5dd] px-2 py-0 text-xs data-[state=off]:border-none data-[state=off]:text-[#757575] data-[state=on]:border-[#02475C] data-[state=on]:bg-[#E9F3F0] data-[state=on]:text-[#02475C]"
              >
                <span className="font-[600] whitespace-nowrap text-xs">Custom message</span>
              </ToggleGroupItem>
              <ToggleGroupItem
                value="template"
                className="flex-1 h-6 rounded-sm border-1 border-solid border-[#d0d5dd] px-2 py-0 text-xs data-[state=off]:border-none data-[state=off]:text-[#757575] data-[state=on]:border-[#02475C] data-[state=on]:bg-[#E9F3F0] data-[state=on]:text-[#02475C]"
              >
                <span className="font-[600] whitespace-nowrap text-xs">Template</span>
              </ToggleGroupItem>
            </ToggleGroup>


            <MessageSection
              channel={channel}
              messageType={messageType}
              fileUploadProps={fileUploadProps}
              onTemplateSelect={(id) => setSelectedTemplateId(Number(id))}
              alertMessage={alertMessage}
              mailSubject={mailSubject}
              setAlertMessage={setAlertMessage}
              setMailSubject={setMailSubject}
              targetGroupId={targetGroupId}
              onTargetGroupChange={setTargetGroupId}
              alertMessageError={errors.alertMessage}
              mailSubjectError={errors.mailSubject}
              targetGroupIdError={errors.targetGroupId}
            />
          </CardContent>


          <div className="mt-9 flex justify-center">
            <Button
              onClick={handleSendAlert}
              disabled={isSending || isLoadingMedia}
              className="rounded-md bg-gradient-to-r from-[#00796B] to-[#004D40] px-5 py-2 text-white transition hover:opacity-90 disabled:opacity-50"
            >
              {isSending ? (
                <>
                  <span className="mr-2">Sending...</span>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                </>
              ) : (
                <>
                  Send Alert{" "}
                  <img className="ml-2 h-4 w-4" src="/send_white.svg" />
                </>
              )}
            </Button>
          </div>
        </Card>
      </div>


      <SuccessDialog
        isOpen={showSuccessDialog}
        onClose={() => setShowSuccessDialog(false)}
      />
    </div>
  );
}
