import React, { useState, useEffect, useMemo } from "react";
import { Search, X, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useAlertTemplates } from "@/api/alertTemplates";
import { AlertTemplate as AlertTemplateType } from "@/types/alertTemplate";

interface AlertTemplate {
  id: string;
  title: string;
  description: string;
  type: string;
  severity: "High Alert" | "Medium Alert" | "Low Alert";
  template_type: string;
  selected?: boolean;
}

const mapSeverity = (
  severity: string,
): "High Alert" | "Medium Alert" | "Low Alert" => {
  switch (severity) {
    case "high":
      return "High Alert";
    case "medium":
      return "Medium Alert";
    case "low":
      return "Low Alert";
    default:
      return "Low Alert";
  }
};

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case "High Alert":
      return "text-red-500 font-semibold";
    case "Medium Alert":
      return "text-[#FFAE19] font-semibold";
    case "Low Alert":
      return "text-teal-600 font-semibold";
    default:
      return "text-gray-500 font-semibold";
  }
};

const getTemplateTypeColor = (templateType: string) => {
  switch (templateType.toLowerCase()) {
    case "sms":
      return "text-black font-semibold";
    case "email":
      return "text-black font-semibold";
    default:
      return "text-black font-semibold";
  }
};

const getHazardIcon = (hazardType: string): string => {
  switch (hazardType.toLowerCase()) {
    case "fire":
      return "/fire.svg";
    case "flood":
      return "/flood.svg";
    case "drought":
      return "/drought.svg";
    case "landslide":
      return "/landslide.svg";
    default:
      return "/icon.svg";
  }
};

interface ChooseTemplateModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  onChoose?: (
    templateId: string,
    templateTitle: string,
    templateContent: string,
  ) => void;
}

export const ChooseTemplateModal: React.FC<ChooseTemplateModalProps> = ({
  isOpen = true,
  onClose = () => {},
  onChoose = () => {},
}) => {
  const { data: apiTemplates, isLoading, isError } = useAlertTemplates();

  const [templates, setTemplates] = useState<AlertTemplate[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTemplateId, setSelectedTemplateId] = useState<
    string | undefined
  >(undefined);
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [severityFilter, setSeverityFilter] = useState<string>("all");
  const [templateTypeFilter, setTemplateTypeFilter] = useState<string>("all");

  const fallbackTemplates: AlertTemplate[] = useMemo(
    () => [
      {
        id: "1",
        title: "Flood Warning",
        description:
          "Heavy rainfall expected. Please take necessary precautions.",
        type: "Flood",
        severity: "High Alert",
        template_type: "EMAIL",
        selected: false,
      },
      {
        id: "2",
        title: "Earthquake Alert",
        description:
          "Seismic activity detected. Stay alert and follow safety protocols.",
        type: "Earthquake",
        severity: "High Alert",
        template_type: "SMS",
        selected: false,
      },
      {
        id: "3",
        title: "Weather Advisory",
        description: "Severe weather conditions expected in your area.",
        type: "Weather",
        severity: "Medium Alert",
        template_type: "EMAIL",
        selected: false,
      },
    ],
    [],
  );

  useEffect(() => {
    if (apiTemplates) {
      const templatesArray = Array.isArray(apiTemplates)
        ? apiTemplates
        : (apiTemplates as { results: AlertTemplateType[] })?.results || [];

      if (templatesArray.length > 0) {
        const formattedTemplates = templatesArray.map(
          (template: AlertTemplateType) => ({
            id: template.id.toString(),
            title: template.template_name,
            description: template.alert_message,
            type:
              template.hazard_type.charAt(0).toUpperCase() +
              template.hazard_type.slice(1),
            severity: mapSeverity(template.severity),
            template_type: template.template_type.toUpperCase(),
            selected: false,
          }),
        );

        setTemplates(formattedTemplates);
      } else {
        setTemplates(fallbackTemplates);
      }

      if (selectedTemplateId) {
        const currentTemplates =
          templatesArray.length > 0
            ? templatesArray.map((t: AlertTemplateType) => ({
                ...t,
                id: t.id.toString(),
              }))
            : fallbackTemplates;

        const templateExists = currentTemplates.some(
          (t) => t.id === selectedTemplateId?.toString(),
        );
        if (!templateExists) {
          setSelectedTemplateId(undefined);
        }
      }
    } else if (isError) {
      console.error("Error fetching alert templates, using fallback data");
      setTemplates(fallbackTemplates);
    }
  }, [apiTemplates, selectedTemplateId, isError, fallbackTemplates]);

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType =
      typeFilter === "all" || template.type.toLowerCase() === typeFilter;

    const matchesSeverity =
      severityFilter === "all" ||
      template.severity.toLowerCase().includes(severityFilter.toLowerCase());

    const matchesTemplateType =
      templateTypeFilter === "all" ||
      template.template_type.toLowerCase() === templateTypeFilter.toLowerCase();

    return (
      matchesSearch && matchesType && matchesSeverity && matchesTemplateType
    );
  });

  const handleSelectTemplate = (id: string) => {
    setSelectedTemplateId(id);
    setTemplates((prev) =>
      prev.map((template) => ({
        ...template,
        selected: template.id === id,
      })),
    );
  };

  const handleChoose = () => {
    const selectedTemplate = templates.find((t) => t.id === selectedTemplateId);
    if (selectedTemplate) {
      onChoose(
        selectedTemplate.id,
        selectedTemplate.title,
        selectedTemplate.description,
      );
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="flex h-[660px] w-[1200px] flex-col gap-4 rounded-lg border border-slate-300 bg-white p-6">
        <div className="flex items-start justify-between">
          <div className="flex flex-col gap-1">
            <h2 className="font-inter text-lg leading-7 font-semibold text-slate-900">
              Choose template
            </h2>
          </div>
          <button
            onClick={onClose}
            className="flex h-8 w-8 items-center justify-center rounded-full p-2.5 hover:bg-gray-100"
          >
            <X className="h-3 w-3 text-gray-500" />
          </button>
        </div>

        <div className="flex flex-1 flex-col gap-6 overflow-hidden">
          <div className="flex items-center justify-center gap-2">
            <div className="relative h-9 w-60">
              <Input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="font-poppins h-full w-full rounded-lg border border-[#d9d9d9] bg-white pr-3 pl-10 text-sm"
              />
              <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
            </div>

            <Select
              value={templateTypeFilter}
              onValueChange={setTemplateTypeFilter}
            >
              <SelectTrigger className="font-poppins relative h-10 w-[140px] rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                <SelectValue
                  placeholder="Channel"
                  className="text-sm font-semibold text-[#9d9d9d]"
                />
                <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555]">
                  arrow_drop_down
                </span>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Channels</SelectItem>
                <SelectItem value="sms">SMS</SelectItem>
                <SelectItem value="email">Email</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="font-poppins relative h-10 w-[140px] rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                <SelectValue
                  placeholder="Hazard Type"
                  className="text-sm font-semibold text-[#9d9d9d]"
                />
                <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555]">
                  arrow_drop_down
                </span>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="flood">Flood</SelectItem>
                <SelectItem value="fire">Fire</SelectItem>
                <SelectItem value="drought">Drought</SelectItem>
                <SelectItem value="landslide">Landslide</SelectItem>
              </SelectContent>
            </Select>

            <Select value={severityFilter} onValueChange={setSeverityFilter}>
              <SelectTrigger className="font-poppins relative h-10 w-[140px] rounded-lg border border-[#d0d5dd] bg-white shadow-none [&>svg]:hidden">
                <SelectValue
                  placeholder="Severity"
                  className="text-sm font-semibold text-[#9d9d9d]"
                />
                <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555]">
                  arrow_drop_down
                </span>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Severities</SelectItem>
                <SelectItem value="high">High Alert</SelectItem>
                <SelectItem value="medium">Medium Alert</SelectItem>
                <SelectItem value="low">Low Alert</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="relative flex flex-1 overflow-hidden">
            <div className="absolute inset-0 overflow-y-auto pr-2">
              {isLoading ? (
                <div className="flex h-full items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin text-[#02475C]" />
                  <span className="ml-2 text-[#02475C]">
                    Loading templates...
                  </span>
                </div>
              ) : isError ? (
                <div className="flex h-full items-center justify-center text-red-500">
                  Error loading templates. Please try again.
                </div>
              ) : filteredTemplates.length === 0 ? (
                <div className="flex h-full items-center justify-center text-gray-500">
                  No templates found matching your criteria
                </div>
              ) : (
                <RadioGroup
                  value={selectedTemplateId}
                  onValueChange={handleSelectTemplate}
                  className="flex flex-col gap-3 pb-2"
                >
                  {filteredTemplates.map((template) => (
                    <div key={template.id} className="flex items-start gap-2">
                      <div className="relative mt-4 inline-flex items-center gap-1">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem
                            value={template.id}
                            id={`radio-${template.id}`}
                            className="text-neutralneutral-1 h-5 w-5"
                          />
                        </div>
                      </div>

                      <div className="flex min-h-[98px] w-full flex-1 items-center gap-8 rounded-md border border-neutral-100 bg-white p-4 shadow-[0px_1px_1px_0px_rgba(174,174,174,0.25)]">
                        <div className="flex flex-1 items-start gap-6">
                          <img
                            src={getHazardIcon(template.type.toLowerCase())}
                            alt="Alert icon"
                            className="h-8 w-8 flex-shrink-0"
                          />

                          <div className="flex flex-1 flex-col gap-1">
                            <h3 className="font-poppins line-clamp-1 text-base leading-normal font-medium text-slate-900">
                              {template.title}
                            </h3>
                            <p className="font-poppins text-sm leading-relaxed break-words text-gray-600">
                              {template.description.length > 120
                                ? template.description.slice(0, 120) + "..."
                                : template.description}
                            </p>
                          </div>
                        </div>

                        <div className="flex flex-shrink-0 items-center gap-4">
                          <div className="flex items-center justify-center rounded-lg border border-[#e9e9e9] px-3 py-2 whitespace-nowrap">
                            <span className="font-poppins text-sm font-semibold text-gray-700">
                              {template.type}
                            </span>
                          </div>
                          <div className="flex items-center justify-center rounded-lg border border-[#e9e9e9] px-3 py-2 whitespace-nowrap">
                            <span
                              className={`font-poppins text-sm ${getTemplateTypeColor(template.template_type)}`}
                            >
                              {template.template_type}
                            </span>
                          </div>
                          <div className="flex h-9 items-center justify-center rounded-lg border border-[#e9e9e9] px-3 py-2 whitespace-nowrap">
                            <span
                              className={`font-poppins text-sm font-medium ${getSeverityColor(template.severity)}`}
                            >
                              {template.severity}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              )}
            </div>
          </div>
        </div>

        <div className="flex h-10 items-center justify-center">
          <button
            onClick={handleChoose}
            disabled={!selectedTemplateId || isLoading}
            className={`flex items-center justify-center rounded-lg px-6 py-2 ${
              !selectedTemplateId || isLoading
                ? "cursor-not-allowed bg-gray-300"
                : "cursor-pointer bg-gradient-to-tl from-[#02475c] to-[#1d8e89]"
            }`}
          >
            <span className="font-poppins text-center text-base font-normal text-white">
              Choose Selected
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};
