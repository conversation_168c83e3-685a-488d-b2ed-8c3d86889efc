"use client";

import { useState } from "react";
// Comment out rich text editor imports
// import { Button } from "@/components/ui/button";
// import {
//   Bold,
//   Italic,
//   Underline,
//   Strikethrough,
//   AlignLeft,
//   AlignCenter,
//   AlignRight,
//   List,
//   ChevronDown,
// } from "lucide-react";
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu";
import { Textarea } from "@/components/ui/textarea";

interface CustomMessageProps {
  value?: string;
  onChange: (value: string) => void;
  error?: string;
}

export function CustomMessage({
  value = "",
  onChange,
  error,
}: CustomMessageProps) {
  const maxLength = 150;
  // Comment out rich text editor state
  // const [fontSize, setFontSize] = useState("14");
  // const [activeFormats, setActiveFormats] = useState<string[]>([]);
  // const [alignment, setAlignment] = useState("left");
  // const [textColor, setTextColor] = useState("#000000");
  const [currentLength, setCurrentLength] = useState(value.length);
  // const editorRef = useRef<HTMLDivElement>(null);

  // const fontSizes = ["10", "12", "14", "16", "18", "20", "24"];
  // const colors = [
  //   "#000000",
  //   "#334155",
  //   "#ef4444",
  //   "#3b82f6",
  //   "#10b981",
  //   "#f59e0b",
  //   "#8b5cf6",
  //   "#ec4899",
  // ];

  // Comment out rich text editor functions
  // const focusEditor = useCallback(() => {
  //   if (editorRef.current) {
  //     editorRef.current.focus();
  //   }
  // }, []);

  // const updateActiveFormats = useCallback(() => {
  //   const formats: string[] = [];

  //   if (document.queryCommandState("bold")) formats.push("bold");
  //   if (document.queryCommandState("italic")) formats.push("italic");
  //   if (document.queryCommandState("underline")) formats.push("underline");
  //   if (document.queryCommandState("strikeThrough"))
  //     formats.push("strikethrough");

  //   setActiveFormats(formats);

  //   if (document.queryCommandState("justifyLeft")) setAlignment("left");
  //   else if (document.queryCommandState("justifyCenter"))
  //     setAlignment("center");
  //   else if (document.queryCommandState("justifyRight")) setAlignment("right");
  // }, []);

  // const updateContentValue = useCallback(() => {
  //   if (editorRef.current) {
  //     const content = editorRef.current.innerHTML;
  //     const textContent = editorRef.current.textContent || "";
  //     setCurrentLength(textContent.length);

  //     if (textContent.length <= maxLength) {
  //       onChange(content);
  //     } else if (editorRef.current.textContent) {
  //       const trimmed = textContent.substring(0, maxLength);
  //       editorRef.current.textContent = trimmed;
  //       onChange(trimmed);
  //       setCurrentLength(maxLength);
  //     }
  //   }
  // }, [onChange, maxLength]);

  // const execCommand = useCallback(
  //   (command: string, value?: string) => {
  //     document.execCommand(command, false, value);
  //     focusEditor();
  //     updateActiveFormats();
  //     updateContentValue();
  //   },
  //   [focusEditor, updateActiveFormats, updateContentValue],
  // );

  // useEffect(() => {
  //   const handleSelectionChange = () => {
  //     updateActiveFormats();
  //   };

  //   document.addEventListener("selectionchange", handleSelectionChange);
  //   return () =>
  //     document.removeEventListener("selectionchange", handleSelectionChange);
  // }, [updateActiveFormats]);

  // useEffect(() => {
  //   if (editorRef.current) {
  //     if (value && editorRef.current.innerHTML !== value) {
  //       editorRef.current.innerHTML = value;
  //     }
  //     document.execCommand("foreColor", false, textColor);
  //   }
  // }, [value, textColor]);

  // // Format button handlers
  // const toggleBold = () => execCommand("bold");
  // const toggleItalic = () => execCommand("italic");
  // const toggleUnderline = () => execCommand("underline");
  // const toggleStrikethrough = () => execCommand("strikeThrough");

  // // Alignment handlers
  // const setAlignLeft = () => {
  //   execCommand("justifyLeft");
  //   setAlignment("left");
  // };
  // const setAlignCenter = () => {
  //   execCommand("justifyCenter");
  //   setAlignment("center");
  // };
  // const setAlignRight = () => {
  //   execCommand("justifyRight");
  //   setAlignment("right");
  // };

  // // Font size handler
  // const handleFontSizeChange = (size: string) => {
  //   setFontSize(size);
  //   execCommand("fontSize", "3");

  //   if (editorRef.current) {
  //     const selection = window.getSelection();
  //     if (selection && selection.rangeCount > 0) {
  //       const range = selection.getRangeAt(0);
  //       if (!range.collapsed) {
  //         const span = document.createElement("span");
  //         span.style.fontSize = `${size}px`;
  //         try {
  //           range.surroundContents(span);
  //         } catch {
  //           span.innerHTML = range.toString();
  //           range.deleteContents();
  //           range.insertNode(span);
  //         }
  //         selection.removeAllRanges();
  //         selection.addRange(range);
  //       }
  //     }
  //   }
  //   focusEditor();
  //   updateContentValue();
  // };

  // // Color handler
  // const handleColorChange = (color: string) => {
  //   setTextColor(color);

  //   const selection = window.getSelection();
  //   if (selection && selection.rangeCount > 0 && !selection.isCollapsed) {
  //     execCommand("foreColor", color);
  //   } else {
  //     document.execCommand("foreColor", false, color);

  //     focusEditor();
  //   }
  // };

  // // Enhanced list handler
  // const toggleBulletList = () => {
  //   const selection = window.getSelection();
  //   if (selection && selection.rangeCount > 0) {
  //     const isInList = document.queryCommandState("insertUnorderedList");
  //     execCommand("insertUnorderedList");

  //     if (!isInList && editorRef.current) {
  //       const lists = editorRef.current.querySelectorAll("ul");
  //       lists.forEach((list) => {
  //         list.style.paddingLeft = "20px";
  //         list.style.marginLeft = "0px";
  //       });
  //     }
  //   }
  //   focusEditor();
  //   updateContentValue();
  // };

  // // Handle paste to maintain formatting
  // const handlePaste = (e: React.ClipboardEvent) => {
  //   e.preventDefault();
  //   const text = e.clipboardData.getData("text/plain");
  //   execCommand("insertText", text);
  //   updateContentValue();
  // };

  // // Handle key shortcuts
  // const handleKeyDown = (e: React.KeyboardEvent) => {
  //   if (e.ctrlKey || e.metaKey) {
  //     switch (e.key) {
  //       case "b":
  //         e.preventDefault();
  //         toggleBold();
  //         break;
  //       case "i":
  //         e.preventDefault();
  //         toggleItalic();
  //         break;
  //       case "u":
  //         e.preventDefault();
  //         toggleUnderline();
  //         break;
  //     }
  //   }

  //   if (e.key.length === 1 && !e.ctrlKey && !e.metaKey) {
  //     setTimeout(() => {
  //       const selection = window.getSelection();
  //       if (selection && selection.rangeCount > 0 && selection.isCollapsed) {
  //         document.execCommand("foreColor", false, textColor);
  //       }
  //     }, 0);
  //   }

  //   // Update content on general typing
  //   setTimeout(updateContentValue, 0);
  // };

  // Handle text change in the simple textarea
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    if (newText.length <= maxLength) {
      onChange(newText);
      setCurrentLength(newText.length);
    }
  };

  return (
    <div className="relative flex w-full flex-col items-start gap-1 self-stretch">
      <div className="relative flex w-full items-center justify-between gap-1 self-stretch">
        <label
          htmlFor="alert-message"
          className="relative mt-[-1.00px] w-fit [font-family:'Poppins-SemiBold',Helvetica] text-sm leading-[18px] tracking-[0] whitespace-nowrap"
        >
          <span className="text-[#434343]">Alert Message </span>
          <span className="text-[#d92525]">*</span>
        </label>
      </div>

      {/* Comment out rich text editor */}
      {/* <div className="w-full overflow-hidden rounded-lg border border-[#d0d5dd] shadow-sm"> */}
      {/* Toolbar */}
      {/* <div className="flex items-center gap-1 border-b border-[#eaecf0] bg-[#f2f4f7] p-2"> */}
      {/* Font Size Dropdown */}
      {/* <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-3 font-medium text-[#334155] hover:bg-[#eaecf0]"
              >
                {fontSize}
                <ChevronDown className="ml-1 h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {fontSizes.map((size) => (
                <DropdownMenuItem
                  key={size}
                  onClick={() => handleFontSizeChange(size)}
                  className="cursor-pointer text-[#334155]"
                >
                  {size}px
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu> */}

      {/* Separator */}
      {/* <div className="mx-1 h-6 w-px bg-[#d0d5dd]" /> */}

      {/* Enhanced Text Color Dropdown */}
      {/* <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-[#eaecf0]"
              >
                <div
                  className="h-4 w-4 rounded-full border border-[#d0d5dd]"
                  style={{ backgroundColor: textColor }}
                />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-48">
              <div className="p-3">
                <div className="mb-3 text-sm font-medium text-[#334155]">
                  Text Colors
                </div>
                <div className="mb-3 grid grid-cols-4 gap-2">
                  {colors.map((color) => (
                    <button
                      key={color}
                      onClick={() => handleColorChange(color)}
                      className="h-8 w-8 rounded-full border-2 shadow-sm transition-transform hover:scale-110"
                      style={{
                        backgroundColor: color,
                        borderColor:
                          textColor === color ? "#334155" : "#d0d5dd",
                      }}
                      title={color}
                    />
                  ))}
                </div>
                <div className="border-t border-[#eaecf0] pt-2">
                  <div className="mb-2 text-xs text-[#667085]">
                    Current Color
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className="h-4 w-4 rounded border border-[#d0d5dd]"
                      style={{ backgroundColor: textColor }}
                    />
                    <span className="font-mono text-xs text-[#334155]">
                      {textColor}
                    </span>
                  </div>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu> */}

      {/* Separator */}
      {/* <div className="mx-1 h-6 w-px bg-[#d0d5dd]" /> */}

      {/* Format Buttons */}
      {/* <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 hover:bg-[#eaecf0] ${activeFormats.includes("bold") ? "bg-[#eaecf0] text-[#101828]" : "text-[#334155]"}`}
            onClick={toggleBold}
            title="Bold (Ctrl+B)"
          >
            <Bold className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 hover:bg-[#eaecf0] ${activeFormats.includes("italic") ? "bg-[#eaecf0] text-[#101828]" : "text-[#334155]"}`}
            onClick={toggleItalic}
            title="Italic (Ctrl+I)"
          >
            <Italic className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 hover:bg-[#eaecf0] ${activeFormats.includes("underline") ? "bg-[#eaecf0] text-[#101828]" : "text-[#334155]"}`}
            onClick={toggleUnderline}
            title="Underline (Ctrl+U)"
          >
            <Underline className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 hover:bg-[#eaecf0] ${activeFormats.includes("strikethrough") ? "bg-[#eaecf0] text-[#101828]" : "text-[#334155]"}`}
            onClick={toggleStrikethrough}
            title="Strikethrough"
          >
            <Strikethrough className="h-4 w-4" />
          </Button> */}

      {/* Separator */}
      {/* <div className="mx-1 h-6 w-px bg-[#d0d5dd]" /> */}

      {/* Alignment Buttons */}
      {/* <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 hover:bg-[#eaecf0] ${alignment === "left" ? "bg-[#eaecf0] text-[#101828]" : "text-[#334155]"}`}
            onClick={setAlignLeft}
            title="Align Left"
          >
            <AlignLeft className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 hover:bg-[#eaecf0] ${alignment === "center" ? "bg-[#eaecf0] text-[#101828]" : "text-[#334155]"}`}
            onClick={setAlignCenter}
            title="Align Center"
          >
            <AlignCenter className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 hover:bg-[#eaecf0] ${alignment === "right" ? "bg-[#eaecf0] text-[#101828]" : "text-[#334155]"}`}
            onClick={setAlignRight}
            title="Align Right"
          >
            <AlignRight className="h-4 w-4" />
          </Button> */}

      {/* Separator */}
      {/* <div className="mx-1 h-6 w-px bg-[#d0d5dd]" /> */}

      {/* <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-[#eaecf0]"
            onClick={toggleBulletList}
            title="Bullet List"
          >
            <List className="h-4 w-4 text-[#334155]" />
          </Button> */}
      {/* </div> */}

      {/* Rich Text Editor */}
      {/* <div
          ref={editorRef}
          contentEditable
          className="h-24 max-h-96 min-h-24 w-full resize-y overflow-auto p-3 font-['Lato'] leading-relaxed text-[#334155] outline-none"
          style={{ fontSize: `${fontSize}px` }}
          onPaste={handlePaste}
          onKeyDown={handleKeyDown}
          onMouseUp={updateActiveFormats}
          onKeyUp={updateActiveFormats}
          onInput={updateContentValue}
          onFocus={() => document.execCommand("foreColor", false, textColor)}
          onClick={() => document.execCommand("foreColor", false, textColor)}
          suppressContentEditableWarning={true}
          data-placeholder="Enter message"
        /> */}
      {/* </div> */}

      {/* Replace with simple textarea */}
      <Textarea
        value={value}
        onChange={handleTextChange}
        placeholder="Enter message"
        className={`h-24 min-h-24 w-full resize-y overflow-auto p-3 font-['Lato'] leading-relaxed text-[#334155] outline-none ${error ? "border border-red-500" : ""}`}
      />
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
      <span className="ml-auto text-[12px] text-gray-500">
        {currentLength}/{maxLength} remaining
      </span>

      {/* Comment out styles */}
      {/* <style
        dangerouslySetInnerHTML={{
          __html: `
        [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9D9D9D;
          pointer-events: none;
        }
        [contenteditable]:focus:before {
          display: none;
        }
      `,
        }}
      /> */}
      {/* <style
        dangerouslySetInnerHTML={{
          __html: `
        [contenteditable] ul {
          list-style-type: disc;
          padding-left: 20px;
          margin: 10px 0;
        }
        [contenteditable] li {
          margin: 5px 0;
        }
        [contenteditable] {
          resize: vertical;
        }
      `,
        }}
      /> */}
    </div>
  );
}
