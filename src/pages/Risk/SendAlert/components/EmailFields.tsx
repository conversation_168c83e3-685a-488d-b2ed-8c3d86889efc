import { Input } from "@/components/ui/input";

import { FileUpload } from "./FileUpload";
import { FileUploadProps } from "../types";
import { CustomMessage } from "./CustomMessage";

interface EmailFieldsProps {
  fileUploadProps: FileUploadProps;
  mailSubject?: string;
  setMailSubject?: (subject: string) => void;
  alertMessage?: string;
  setAlertMessage?: (message: string) => void;
  mailSubjectError?: string;
  alertMessageError?: string;
}

export function EmailFields({
  fileUploadProps,
  mailSubject = "",
  setMailSubject = () => {},
  alertMessage = "",
  setAlertMessage = () => {},
  mailSubjectError,
  alertMessageError,
}: EmailFieldsProps) {
  return (
    <>

      <div className="relative flex w-full flex-[0_0_auto] flex-col items-start gap-1 self-stretch">
        <label className="relative flex w-full flex-[0_0_auto] items-center gap-1 self-stretch">
          <span className="relative mt-[-1.00px] w-fit [font-family:'Poppins-SemiBold',Helvetica] text-sm leading-[18px] tracking-[0] whitespace-nowrap text-[#434343]">
            Mail Subject <span className="text-[#d92525]">*</span>
          </span>
        </label>
        <Input
          required
          value={mailSubject}
          onChange={(e) => setMailSubject(e.target.value)}
          placeholder="Enter Mail Subject"
          className={`bg-white-900 placeholder:text-greygray-400 relative h-10 w-full items-center gap-2 self-stretch rounded-lg border border-solid p-3 [font-family:'Poppins-SemiBold',Helvetica] text-sm leading-[18px] tracking-[0] ${mailSubjectError ? "border-red-500" : "border-[#d0d5dd]"}`}
        />
        {mailSubjectError && (
          <p className="mt-1 text-xs text-red-500">{mailSubjectError}</p>
        )}
      </div>

      <CustomMessage
        value={alertMessage}
        onChange={setAlertMessage}
        error={alertMessageError}
      />

      <FileUpload {...fileUploadProps} />
    </>
  );
}
