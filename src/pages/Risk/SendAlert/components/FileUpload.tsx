"use client";

import { useState, useEffect } from "react";
import { Upload, X, Download, Trash2, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { FileUploadProps } from "../types";

interface UploadingFile {
  file: File;
  isUploading: boolean;
}

interface ExtendedFileUploadProps extends FileUploadProps {
  showLabel?: boolean;
  isLoading?: boolean;
}

export function FileUpload({
  selectedFiles,
  dragOver,
  onFileClick,
  onFileChange,
  onDrop,
  onDragOver,
  onDragLeave,
  onFileDelete,
  fileInputRef,
  showLabel = true,
  isLoading = false,
}: ExtendedFileUploadProps) {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);

  useEffect(() => {

    const newFiles = selectedFiles.filter(
      (file) =>
        !uploadingFiles.some(
          (uf) =>
            uf.file.name === file.name && uf.file.size === file.size,
        ),
    );

    if (newFiles.length > 0) {

      setUploadingFiles((prev) => [
        ...prev,
        ...newFiles.map((file) => ({ file, isUploading: true })),
      ]);


      newFiles.forEach((file) => {
        setTimeout(() => {
          setUploadingFiles((prev) =>
            prev.map((uf) =>
              uf.file.name === file.name && uf.file.size === file.size
                ? { ...uf, isUploading: false }
                : uf,
            ),
          );
        }, 2000);
      });
    }
  }, [selectedFiles, uploadingFiles]);

  useEffect(() => {
    setUploadingFiles((prev) =>
      prev.filter((uf) =>
        selectedFiles.some(
          (file) => file.name === uf.file.name && file.size === uf.file.size,
        ),
      ),
    );
  }, [selectedFiles]);

  return (
    <div className="w-full">
      <div className="relative flex w-full flex-col items-start gap-1 self-stretch">
        {showLabel && (
          <label className="relative flex w-full items-center gap-1 self-stretch">
            <span className="relative mt-[-1.00px] w-fit [font-family:'Poppins-SemiBold',Helvetica] text-sm leading-[18px] tracking-[0] whitespace-nowrap text-[#434343]">
              Attachments <span className="text-[#d92525]">*</span>
            </span>
          </label>
        )}


        <div
          className={`h-[100px] w-full rounded-lg border-2 border-dashed p-12 text-center transition-colors ${
            dragOver
              ? "border-[#02475c] bg-[#e9f3f0]"
              : "border-[#d0d5dd] bg-[#fafafa]"
          }`}
          onClick={onFileClick}
          onDrop={onDrop}
          onDragOver={onDragOver}
          onDragLeave={onDragLeave}
        >
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-4">
              <Loader2 className="h-5 w-5 animate-spin text-[#02475C]" />
              <p className="mt-2 text-sm text-[#434343]">
                Loading attachment...
              </p>
            </div>
          ) : (
            <>
              <Upload className="mx-auto my-[-20px] mb-5 h-4 w-4 text-[#7b7b7b]" />
              <p className="text-base text-[#7b7b7b]">
                Drop here to attach or{" "}
                <button className="text-[#02475c]">upload</button>
              </p>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                className="hidden"
                onChange={onFileChange}
              />
            </>
          )}
        </div>


        <p className="mt-3 mb-6 text-sm text-[#dc2626]">Max file size: 1GB</p>


        {uploadingFiles.length > 0 && (
          <div className="w-full space-y-3">
            {uploadingFiles.map(({ file, isUploading }) => (
              <div
                key={file.name + file.size}
                className="flex items-center gap-4 rounded-lg border border-[#d0d5dd] bg-[#ffffff] p-4"
              >
                {isUploading ? (
                  <>
                    <div className="relative">
                      <div className="h-8 w-8 rounded-full border-2 border-[#e9f3f0]">
                        <div className="absolute top-0 left-0 h-8 w-8 animate-spin rounded-full border-2 border-[#02475c] border-t-transparent"></div>
                      </div>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-[#434343]">{file.name}</p>
                      <p className="text-sm text-[#7b7b7b]">Uploading...</p>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-[#6e6e6e] hover:text-[#434343]"
                      onClick={() => onFileDelete(file)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </>
                ) : (
                  <>
                    <div className="flex items-center justify-center">
                      <img
                        src="/file.svg"
                        alt="file icon"
                        className="h-11 w-11"
                      />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-[#434343]">{file.name}</p>
                      <p className="text-sm text-[#7b7b7b]">
                        {new Date().toLocaleDateString("en-US", {
                          month: "short",
                          day: "2-digit",
                          year: "numeric",
                        })}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-[#6e6e6e] hover:text-[#434343]"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-[#6e6e6e] hover:text-[#434343]"
                        onClick={() => onFileDelete(file)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
