import { Channel, MessageType, FileUploadProps } from "../types";
import { CustomMessage } from "./CustomMessage";
import { EmailFields } from "./EmailFields";
import { TargetGroupSelect } from "./TargetGroupSelect";
import { TemplateSelector } from "./TemplateSelector";

interface MessageSectionProps {
  channel: Channel;
  messageType: MessageType;
  fileUploadProps: FileUploadProps;
  onTemplateSelect?: (templateId: string) => void;
  alertMessage?: string;
  mailSubject?: string;
  setAlertMessage?: (message: string) => void;
  setMailSubject?: (subject: string) => void;
  targetGroupId?: number | null;
  onTargetGroupChange?: (groupId: number) => void;
  alertMessageError?: string;
  mailSubjectError?: string;
  targetGroupIdError?: string;
}

export function MessageSection({
  channel,
  messageType,
  fileUploadProps,
  onTemplateSelect,
  alertMessage,
  mailSubject,
  setAlertMessage,
  setMailSubject,
  targetGroupId,
  onTargetGroupChange,
  alertMessageError,
  mailSubjectError,
  targetGroupIdError,
}: MessageSectionProps) {

  if (channel === "sms") {
    return (
      <>
        {messageType === "template" ? (
          <>
            <TemplateSelector onTemplateSelect={onTemplateSelect} />
            <CustomMessage
              value={alertMessage}
              onChange={setAlertMessage || (() => {})}
              error={alertMessageError}
            />
            <TargetGroupSelect
              value={targetGroupId}
              onChange={onTargetGroupChange}
              error={targetGroupIdError}
            />
          </>
        ) : (
          <>
            <CustomMessage
              value={alertMessage}
              onChange={setAlertMessage || (() => {})}
              error={alertMessageError}
            />
            <TargetGroupSelect
              value={targetGroupId}
              onChange={onTargetGroupChange}
              error={targetGroupIdError}
            />
          </>
        )}
      </>
    );
  }


  return (
    <>
      {messageType === "template" ? (
        <>
          <TemplateSelector onTemplateSelect={onTemplateSelect} />
          <EmailFields
            fileUploadProps={fileUploadProps}
            mailSubject={mailSubject}
            setMailSubject={setMailSubject}
            alertMessage={alertMessage}
            setAlertMessage={setAlertMessage}
            mailSubjectError={mailSubjectError}
            alertMessageError={alertMessageError}
          />
          <TargetGroupSelect
            value={targetGroupId}
            onChange={onTargetGroupChange}
            error={targetGroupIdError}
          />
        </>
      ) : (
        <>
          <EmailFields
            fileUploadProps={fileUploadProps}
            mailSubject={mailSubject}
            setMailSubject={setMailSubject}
            alertMessage={alertMessage}
            setAlertMessage={setAlertMessage}
            mailSubjectError={mailSubjectError}
            alertMessageError={alertMessageError}
          />
          <TargetGroupSelect
            value={targetGroupId}
            onChange={onTargetGroupChange}
            error={targetGroupIdError}
          />
        </>
      )}
    </>
  );
}
