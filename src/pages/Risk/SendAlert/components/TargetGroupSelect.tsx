import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useGetGroups } from "@/api/groups";
import { CategoryEnum, Group, SectorEnum } from "@/types/group";
import { Loader2 } from "lucide-react";
import { useState, useEffect } from "react";

interface TargetGroupSelectProps {
  value?: number | null;
  onChange?: (groupId: number) => void;
  error?: string;
}

export function TargetGroupSelect({
  value,
  onChange,
  error,
}: TargetGroupSelectProps) {
  const [useFallbackData, setUseFallbackData] = useState(false);


  const {
    data: groupsData,
    isLoading,
    isError,
  } = useGetGroups({
    category: CategoryEnum.GROUP,
  });


  const fallbackGroups: Group[] = [
    {
      id: 1,
      group_name: "Ward 1 - Health",
      location: "Sudurpashim",
      sector: SectorEnum.EDUCATION,
      category: CategoryEnum.GROUP,
    },
    {
      id: 2,
      group_name: "Group 2",
      location: "Kathmandu",
      sector: SectorEnum.HEALTH,
      category: CategoryEnum.GROUP,
    },
    {
      id: 3,
      group_name: "Mahakali",
      location: "Kanchanpur",
      sector: SectorEnum.GOVERNMENT,
      category: CategoryEnum.GROUP,
    },
    {
      id: 4,
      group_name: "Narayani",
      location: "Chitwan",
      sector: SectorEnum.EDUCATION,
      category: CategoryEnum.GROUP,
    },
  ];


  useEffect(() => {
    if (isError) {
      setUseFallbackData(true);
    }
  }, [isError]);


  const groups: Group[] = useFallbackData
    ? fallbackGroups
    : Array.isArray(groupsData)
      ? groupsData
      : groupsData?.results || [];

  const handleValueChange = (value: string) => {
    if (onChange && value) {
      onChange(Number(value));
    }
  };

  return (
    <div className="relative flex w-full flex-col items-start gap-1 self-stretch">
      <div className="relative flex w-full items-center gap-1 self-stretch">
        <label className="relative mt-[-1.00px] w-fit [font-family:'Poppins-SemiBold',Helvetica] text-sm leading-[18px] tracking-[0] whitespace-nowrap">
          <span className="text-[#434343]">Target Group </span>
          <span className="text-[#d92525]">*</span>
        </label>
      </div>

      <Select value={value?.toString()} onValueChange={handleValueChange}>
        <SelectTrigger
          className={`font-poppins relative h-10 w-full rounded-lg border bg-white shadow-none [&>svg]:hidden ${error ? "border-red-500" : "border-[#d0d5dd]"}`}
        >
          <SelectValue
            placeholder={
              isLoading ? "Loading groups..." : "Choose Target Group"
            }
            className="text-sm font-semibold text-[#9d9d9d]"
          />
          <span className="material-symbols-outlined pointer-events-none absolute top-1/2 right-2 -translate-y-1/2 text-[20px] font-normal text-[#555555] placeholder:text-[#9d9d9d]">
            arrow_drop_down
          </span>
        </SelectTrigger>
        <SelectContent>
          {isLoading ? (
            <div className="flex items-center justify-center p-2">
              <Loader2 className="h-4 w-4 animate-spin text-[#1D8E89]" />
              <span className="ml-2 text-sm">Loading groups...</span>
            </div>
          ) : isError ? (
            <div>
              <SelectItem value="error" disabled>
                Error loading groups
              </SelectItem>
              <SelectItem
                value="use-fallback"
                onClick={() => setUseFallbackData(true)}
              >
                Use demo data
              </SelectItem>
            </div>
          ) : groups.length === 0 ? (
            <SelectItem value="empty" disabled>
              No groups available
            </SelectItem>
          ) : (
            groups.map((group: Group) => (
              <SelectItem key={group.id} value={group.id.toString()}>
                {group.group_name || `Group ${group.id}`} - {group.location}
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  );
}
