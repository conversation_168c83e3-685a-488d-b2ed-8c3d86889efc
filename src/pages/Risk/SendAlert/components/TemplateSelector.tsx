import { useState } from "react";
import { Button } from "@/components/ui/button";
import { ChooseTemplateModal } from "./ChooseTemplateModal";

import { Badge } from "@/components/ui/badge";
import { Check } from "lucide-react";

interface TemplateSelectorProps {
  onTemplateSelect?: (templateId: string) => void;
}

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  onTemplateSelect,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<{
    id: string;
    title: string;
  } | null>(null);

  const handleOpenModal = () => setIsModalOpen(true);
  const handleCloseModal = () => setIsModalOpen(false);
  const handleChooseTemplate = (templateId: string, templateTitle: string) => {
    setSelectedTemplate({ id: templateId, title: templateTitle });
    onTemplateSelect?.(templateId);
    setIsModalOpen(false);
  };

  return (
    <>
      <div className="relative flex w-full flex-[0_0_auto] items-center gap-3 self-stretch">
        <label className="relative inline-flex flex-[0_0_auto] items-center gap-1">
          <span className="relative mt-[-1.00px] w-fit [font-family:'Poppins-SemiBold',Helvetica] text-sm leading-[18px] tracking-[0] whitespace-nowrap text-[#434343]">
            Template <span className="text-[#d92525]">*</span>
          </span>
        </label>

        <Button
          variant="outline"
          onClick={handleOpenModal}
          className="border-primary-1 text-primary-1 relative inline-flex h-auto flex-[0_0_auto] items-center gap-2 rounded-lg border border-solid border-[#02475C] px-6 py-2 text-center [font-family:'Poppins-Regular',Helvetica] text-sm leading-[normal] font-normal tracking-[0]"
        >
          {selectedTemplate ? (
            <>
              <Check className="h-4 w-4 text-green-500" />
              <span>Template Selected</span>
              <Badge
                variant="outline"
                className="ml-2 border-green-200 bg-green-50 text-green-700"
              >
                {selectedTemplate.title}
              </Badge>
            </>
          ) : (
            "Choose Template"
          )}
        </Button>
      </div>

      <ChooseTemplateModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onChoose={handleChooseTemplate}
      />
    </>
  );
};
