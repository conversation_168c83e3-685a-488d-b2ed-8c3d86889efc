import React, { useState } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";

interface ThresholdRangeProps {
  onChange?: (values: { min: number; max: number }) => void;
}

const ThresholdRange: React.FC<ThresholdRangeProps> = ({ onChange }) => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [values, setValues] = useState({ min: 0, max: 0 });

  const handleValueChange = (type: "min" | "max", value: number) => {
    const newValues = {
      ...values,
      [type]: value,
    };
    setValues(newValues);
    onChange?.(newValues);
  };

  return (
    <div className="flex items-center gap-3">

      <div className="inline-flex items-center gap-[7px]">
        <Checkbox
          id="threshold-range-checkbox"
          className="text-primary-1"
          checked={isEnabled}
          onCheckedChange={(checked: boolean) => setIsEnabled(checked)}
        />
        <label
          htmlFor="threshold-range-checkbox"
          className="font-poppins text-sm text-[#434343]"
        >
          Use Range
        </label>
      </div>


      <div className="flex flex-1 items-start gap-3">
        <Input
          type="number"
          min={0}
          max={values.max}
          value={values.min}
          onChange={(e) =>
            handleValueChange("min", Math.max(0, Number(e.target.value)))
          }
          className="h-10 w-[140px] rounded-lg border border-[#d0d5dd] bg-white px-3 text-sm text-[#555555]"
          disabled={!isEnabled}
          placeholder="Min"
        />
        <Input
          type="number"
          min={values.min}
          value={values.max}
          onChange={(e) =>
            handleValueChange(
              "max",
              Math.max(values.min, Number(e.target.value)),
            )
          }
          className="h-10 w-[140px] rounded-lg border border-[#d0d5dd] bg-white px-3 text-sm text-[#555555]"
          disabled={!isEnabled}
          placeholder="Max"
        />
      </div>
    </div>
  );
};

export default ThresholdRange;
