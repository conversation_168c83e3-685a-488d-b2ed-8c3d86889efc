import { Navigate, Route, Routes } from "react-router-dom";
import ManualAlert from "./ManualAlert";
import AutomaticAlert from "./AutomaticAlert";

export default function SendAlert() {
  return (
    <Routes>
      <Route index element={<Navigate to="manual" replace />} />
      <Route path="manual" element={<ManualAlert />} />
      <Route path="automatic" element={<AutomaticAlert />} />
    </Routes>
  );
}
