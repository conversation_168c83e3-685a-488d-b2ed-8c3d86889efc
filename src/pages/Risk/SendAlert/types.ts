export type Channel = "sms" | "email";
export type MessageType = "template" | "custom";
export type HazardType = "flood" | "landslide" | "drought" | "fire" | "other";

export interface FileUploadProps {
  selectedFiles: File[];
  dragOver: boolean;
  onFileClick: () => void;
  onFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onDrop: (event: React.DragEvent<HTMLDivElement>) => void;
  onDragOver: (event: React.DragEvent<HTMLDivElement>) => void;
  onDragLeave: (event: React.DragEvent<HTMLDivElement>) => void;
  onFileDelete: (file: File) => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  isLoading?: boolean;
}


export interface SendAlertRequest {
  hazard_type: HazardType;
  channel: Channel;
  template_type: MessageType;
  alert_message?: string;
  mail_subject?: string;
  file_upload?: File;
  template?: number;
  target_group?: number;
}


export interface SendAlert {
  id: number;
  hazard_type: HazardType;
  channel: Channel;
  template_type: MessageType;
  alert_message?: string;
  mail_subject?: string;
  file_upload?: string;
  created_at: string;
  template?: number;
  target_group?: number;
}
