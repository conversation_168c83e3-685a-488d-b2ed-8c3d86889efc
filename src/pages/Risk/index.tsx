import GenerateRoutes from "../../generateRoutes";
import dashboardRoutes from "../../routes/dashboard";
import Layout from "../../shared/Layout";
import ProtectedRoute from "@/components/ProtectedRoute";

export default function Risk() {
  return (
    <ProtectedRoute>
      <Layout>
        <div className="h-full max-h-screen">
          {GenerateRoutes({ routes: dashboardRoutes })}
        </div>
      </Layout>
    </ProtectedRoute>
  );
}
