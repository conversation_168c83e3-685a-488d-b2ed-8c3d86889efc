import { lazy } from "react";
import { IRoute } from "../types/route";

const Dashboard = lazy(() => import("../pages/Risk/DashBoard"));
const RealTimeMonitoring = lazy(() => import("../pages/Risk/RealTimeMonitoring"));
const AlertTemplate = lazy(
  () => import("../pages/Risk/AlertTemplate/AllAlerts"),
);
const SMSAlertTemplate = lazy(
  () => import("../pages/Risk/AlertTemplate/SMSAlertTemplate"),
);
const EmailAlertTemplate = lazy(
  () => import("../pages/Risk/AlertTemplate/EmailAlertTemplate"),
);
const IVRAlertTemplate = lazy(
  () => import("../pages/Risk/AlertTemplate/IVRAlertTemplate"),
);
const Groups = lazy(() => import("../pages/Risk/Groups"));
const Group = lazy(() => import("../pages/Risk/Groups/Group"));
const SendAlert = lazy(() => import("../pages/Risk/SendAlert"));
const ManualAlert = lazy(() => import("../pages/Risk/SendAlert/ManualAlert"));
const AutomaticAlert = lazy(
  () => import("../pages/Risk/SendAlert/AutomaticAlert"),
);
const AlertLogs = lazy(() => import("../pages/Risk/AlertLogs"));

export const dashboardRoutes: IRoute[] = [
  {
    id: 1,
    path: "/dashboard",
    name: "Dashboard",
    component: Dashboard,
  },
  {
    id: 2,
    path: "/real-time-monitoring",
    name: "Real Time Monitoring",
    component: RealTimeMonitoring,
  },
  {
    id: 3,
    path: "/alert-template",
    name: "Alert Template",
    component: AlertTemplate,
  },
  {
    id: 4,
    path: "/alert-template/sms",
    name: "SMS Alert Template",
    component: SMSAlertTemplate,
  },
  {
    id: 5,
    path: "/alert-template/email",
    name: "Email Alert Template",
    component: EmailAlertTemplate,
  },
  {
    id: 12,
    path: "/alert-template/ivr",
    name: "IVR Alert Template",
    component: IVRAlertTemplate,
  },
  {
    id: 6,
    path: "/groups",
    name: "Groups",
    component: Groups,
  },
  {
    id: 7,
    path: "/groups/group",
    name: "Group",
    component: Group,
  },
  {
    id: 8,
    path: "/send-alert",
    name: "Send Alert",
    component: SendAlert,
  },
  {
    id: 9,
    path: "/send-alert/manual",
    name: "Manual Alert",
    component: ManualAlert,
  },
  {
    id: 10,
    path: "/send-alert/automatic",
    name: "Automatic Alert",
    component: AutomaticAlert,
  },
  {
    id: 11,
    path: "/alert-logs",
    name: "Alert Logs",
    component: AlertLogs,
  },
];

export default dashboardRoutes;
