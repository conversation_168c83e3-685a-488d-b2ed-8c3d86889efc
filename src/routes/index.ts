import { lazy } from "react";
import { IRoute } from "../types/route";

const Home = lazy(() => import("../pages/Home"));
const About = lazy(() => import("../pages/About"));
const Blogs = lazy(() => import("../pages/Blogs"));
const GetInvolve = lazy(() => import("../pages/GetInvolve"));
const BlogDetail = lazy(() => import("../pages/Blogs/BlogDetail"));
const Risk = lazy(() => import("../pages/Risk"));
const NotFound = lazy(() => import("../pages/Error/NotFound"));

const appRoutes: IRoute[] = [
  { id: 1, path: "/", name: "Home", component: Home },
  { id: 2, path: "/about", name: "About", component: About },
  { id: 3, path: "/news", name: "Blogs", component: Blogs },
  { id: 4, path: "/getInvolved", name: "Services", component: GetInvolve },
  { id: 5, path: "/blogs/:slug", name: "Blog Detail", component: BlogDetail },
  { id: 6, path: "/risk/*", name: "Risk", component: Risk },
  { id: 7, path: "*", name: "Not Found", component: NotFound },
];

export default appRoutes;
