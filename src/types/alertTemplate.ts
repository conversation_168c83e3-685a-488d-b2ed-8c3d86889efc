import { Media } from "./media";

export enum TemplateType {
  SMS = "sms",
  EMAIL = "email",
}

export enum HazardType {
  FIRE = "fire",
  DROUGHT = "drought",
  FLOOD = "flood",
  LANDSLIDE = "landslide",
}

export enum Severity {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
}

export interface AlertTemplate {
  id: number;
  media: Media;
  template_type: TemplateType;
  template_name: string;
  hazard_type: HazardType;
  severity: Severity;
  alert_message: string;
  mail_subject?: string;
  created_at: string;
}

export interface AlertTemplateRequest {
  media_id: number;
  template_type: TemplateType;
  template_name: string;
  hazard_type: HazardType;
  severity: Severity;
  alert_message: string;
  mail_subject?: string;
}
