export enum CategoryEnum {
  INDIVIDUAL = "individual",
  GROUP = "group",
}

export enum SectorEnum {
  GOVERNMENT = "government",
  EDUCATION = "education",
  HEALTH = "health",
  AGRICULTURE = "agriculture",
  WATER_SUPPLY = "water_supply",
}

export interface Group {
  id: number;
  category: CategoryEnum;
  group_name?: string;
  location: string;
  sector?: SectorEnum | null;
  contacts_file?: string;
}

export interface GroupRequest {
  category: CategoryEnum;
  group_name?: string;
  location: string;
  sector?: SectorEnum | null;
  contacts_file?: File;
}

export interface GroupResponse extends Group {
  id: number;
  created_at: string;
  updated_at: string;
}

export interface GroupMember {
  id: number;
  user_id: number;
  email: string;
  phone_number?: string;
  name: string;
  role: GroupRole;
  joined_at: string;
}

export enum GroupRole {
  ADMIN = "admin",
  MEMBER = "member",
  VIEWER = "viewer",
}

export interface CreateGroupRequest {
  name: string;
  description?: string;
  members: {
    email: string;
    phone_number?: string;
    name: string;
    role: GroupRole;
  }[];
}

export interface UpdateGroupRequest {
  name?: string;
  description?: string;
}

export interface AddGroupMemberRequest {
  email: string;
  phone_number?: string;
  name: string;
  role: GroupRole;
}

export interface UpdateGroupMemberRequest {
  role?: GroupRole;
  phone_number?: string;
  name?: string;
}
