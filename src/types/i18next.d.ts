import "react-i18next";

declare module "react-i18next" {
  interface CustomTypeOptions {
    defaultNS: "translation";
    resources: {
      translation: {
        belowWarning: string;
        warning: string;
        danger: string;
        alertSent: string;

        overall: string;
        riverWatch: string;
        rainfallWatch: string;

        searchStations: string;

        loadingMap: string;
        failedToLoad: string;
        retry: string;

        dataSource: string;
        lastUpdated: string;
        rainfallUpdated: string;
        riverUpdated: string;

        riverWatchTitle: string;
        waterLevel: string;
        status: string;
        normal: string;

        rainfallWatchTitle: string;
        rainfall: string;
        measurement: string;

        temperature: string;
        humidity: string;
        windSpeed: string;
        pressure: string;

        loading: string;
        noData: string;
        error: string;
        refresh: string;

        dashboard: string;
        realTimeMonitoring: string;
        alertTemplate: string;
        groups: string;
        sendAlert: string;
        all: string;
        sms: string;
        email: string;
        ivr: string;
        manualAlert: string;
        automaticAlert: string;
        alertLogs: string;

        legendWaterLevel: string;
        legendRainfall: string;

        searchRivers: string;
        searchRainfall: string;
        noRiversFound: string;
        noRainfallFound: string;
        result: string;
        results: string;

        "1hr": string;
        "3hr": string;
        "6hr": string;
        "12hr": string;
        "24hr": string;

        showAll: string;
        showing: string;
        of: string;
        locations: string;

        weatherLoading: string;
        partialCloudy: string;
        noWeatherData: string;
        noRainfallData: string;

        realTimeMonitoringTitle: string;
        totalStations: string;
        search: string;

        serialNumber: string;
        basinName: string;
        stationIndex: string;
        stationName: string;
        waterLevelM: string;
        warningLevelM: string;
        dangerLevelM: string;
        tableStatus: string;

        loadingRainfallData: string;
        loadingWaterLevelData: string;
        failedToFetchRainfall: string;
        failedToFetchWaterLevel: string;
        pleaseRetryLater: string;
        noDataFound: string;
        unknown: string;
        unknownStation: string;

        warningNote: string;
        mmIn1hr: string;
        mmIn3hr: string;
        mmIn6hr: string;
        mmIn12hr: string;
        mmIn24hr: string;

        home: string;
        about: string;
        digest: string;
        getInvolved: string;
        login: string;
        logout: string;

        stayUpToDate: string;
        newsletterDescription: string;
        enterYourEmail: string;
        subscribe: string;
        technicalPartner: string;
        designAssociatesNepal: string;
      };
    };
  }
}
