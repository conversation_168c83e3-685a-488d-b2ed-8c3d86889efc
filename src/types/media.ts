export enum MediaType {
  IMAGE = "image",
  VIDEO = "video",
  DOCUMENT = "document",
  AUDIO = "audio",
  OTHER = "other",
}

export interface Media {
  id: number;
  file: string; 
  media_type: MediaType;
  title: string;
  description?: string;
  uploaded_at: string;
  is_public: boolean;
  owner?: number;
}

export interface MediaRequest {
  file: File;
  media_type: MediaType;
  title: string;
  description?: string;
  is_public?: boolean;
  owner?: number;
}
