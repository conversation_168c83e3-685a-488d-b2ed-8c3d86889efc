export enum HazardType {
  FLOOD = "flood",
  LANDSLIDE = "landslide",
  DROUGHT = "drought",
  FIRE = "fire",
}

export enum Channel {
  SMS = "sms",
  EMAIL = "email",
}

export enum TemplateType {
  CUSTOM = "custom",
  TEMPLATE = "template",
}

export interface SendAlert {
  id: number;
  hazard_type: HazardType;
  channel: Channel;
  template_type: TemplateType;
  alert_message: string | null;
  mail_subject: string | null;
  file_upload: string | null;
  created_at: string;
  template: number | null;
  target_group: number | null;
}

export interface SendAlertRequest {
  hazard_type: HazardType;
  channel: Channel;
  template_type: TemplateType;
  alert_message?: string;
  mail_subject?: string;
  file_upload?: File;
  template?: number;
  target_group?: number;
}

export interface PatchedSendAlertRequest {
  hazard_type?: HazardType;
  channel?: Channel;
  template_type?: TemplateType;
  alert_message?: string;
  mail_subject?: string;
  file_upload?: File;
  template?: number;
  target_group?: number;
}
