/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_SENTRY_DSN: string;
  readonly VITE_VERSION: string;
  readonly VITE_API_BASE_URL: string;
  readonly VITE_EMAILJS_SERVICE_ID: string;
  readonly VITE_EMAILJS_TEMPLATE_ID: string;
  readonly VITE_EMAILJS_PUBLIC_KEY: string;
  readonly VITE_WEATHERAPI_KEY: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
